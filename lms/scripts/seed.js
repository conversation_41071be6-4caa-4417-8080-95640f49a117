const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create sample users
  const hashedPassword = await bcrypt.hash('password123', 12)

  // Create instructor
  const instructor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>ructor',
      password: hashedPassword,
      role: 'INSTRUCTOR',
      bio: 'Experienced software engineer and educator with 10+ years in the industry.',
    },
  })

  // Create student
  const student = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: hashedPassword,
      role: 'STUDENT',
      studentProfile: {
        create: {
          education: 'Computer Science Graduate',
          experience: 'Entry Level',
          skills: JSON.stringify(['JavaScript', 'React', 'Node.js']),
          targetRole: 'Frontend Developer',
          experienceLevel: 'BEGINNER',
        }
      }
    },
  })

  // Create sample courses
  const course1 = await prisma.course.upsert({
    where: { id: 'sample-course-1' },
    update: {},
    create: {
      id: 'sample-course-1',
      title: 'Complete React Development Course',
      description: 'Learn React from basics to advanced concepts including hooks, context, and state management.',
      category: 'Programming',
      level: 'INTERMEDIATE',
      price: 99.99,
      duration: 40,
      language: 'English',
      isPublished: true,
      instructorId: instructor.id,
    },
  })

  const course2 = await prisma.course.upsert({
    where: { id: 'sample-course-2' },
    update: {},
    create: {
      id: 'sample-course-2',
      title: 'AI and Machine Learning Fundamentals',
      description: 'Introduction to artificial intelligence and machine learning concepts with practical examples.',
      category: 'Data Science',
      level: 'BEGINNER',
      price: 149.99,
      duration: 60,
      language: 'English',
      isPublished: true,
      instructorId: instructor.id,
    },
  })

  const course3 = await prisma.course.upsert({
    where: { id: 'sample-course-3' },
    update: {},
    create: {
      id: 'sample-course-3',
      title: 'Free JavaScript Basics',
      description: 'Learn JavaScript fundamentals for free. Perfect for beginners starting their coding journey.',
      category: 'Programming',
      level: 'BEGINNER',
      price: null,
      duration: 20,
      language: 'English',
      isPublished: true,
      instructorId: instructor.id,
    },
  })

  // Create course modules and lessons
  const module1 = await prisma.courseModule.create({
    data: {
      courseId: course1.id,
      title: 'React Fundamentals',
      description: 'Learn the basics of React including components, props, and state.',
      orderIndex: 1,
    },
  })

  await prisma.lesson.create({
    data: {
      courseId: course1.id,
      moduleId: module1.id,
      title: 'Introduction to React',
      description: 'What is React and why use it?',
      content: 'React is a JavaScript library for building user interfaces...',
      duration: 15,
      orderIndex: 1,
      lessonType: 'VIDEO',
    },
  })

  await prisma.lesson.create({
    data: {
      courseId: course1.id,
      moduleId: module1.id,
      title: 'Creating Your First Component',
      description: 'Learn how to create and use React components.',
      content: 'Components are the building blocks of React applications...',
      duration: 20,
      orderIndex: 2,
      lessonType: 'VIDEO',
    },
  })

  // Create sample reviews
  await prisma.courseReview.create({
    data: {
      courseId: course1.id,
      userId: student.id,
      rating: 5,
      review: 'Excellent course! Very well explained and practical examples.',
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log('📧 Sample accounts created:')
  console.log('   Instructor: <EMAIL> / password123')
  console.log('   Student: <EMAIL> / password123')
  console.log('📚 Sample courses created')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
