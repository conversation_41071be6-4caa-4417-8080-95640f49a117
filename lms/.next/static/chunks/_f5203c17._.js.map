{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/mock-interview/%5BsessionId%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useParams } from 'next/navigation'\nimport {\n  VideoCameraIcon,\n  MicrophoneIcon,\n  StopIcon,\n  PlayIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline'\n\ninterface Question {\n  id: string\n  question: string\n  type: string\n  category: string\n  orderIndex: number\n}\n\ninterface Evaluation {\n  score: number\n  feedback: string\n  keyPoints: string[]\n  suggestions: string[]\n}\n\nexport default function InterviewSessionPage() {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const params = useParams()\n  const sessionId = params.sessionId as string\n\n  const [isLoading, setIsLoading] = useState(true)\n  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)\n  const [answer, setAnswer] = useState('')\n  const [isRecording, setIsRecording] = useState(false)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [evaluation, setEvaluation] = useState<Evaluation | null>(null)\n  const [questionNumber, setQuestionNumber] = useState(1)\n  const [totalQuestions, setTotalQuestions] = useState(0)\n  const [timeRemaining, setTimeRemaining] = useState(0)\n  const [sessionStarted, setSessionStarted] = useState(false)\n  const [isComplete, setIsComplete] = useState(false)\n\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null)\n  const recordedChunksRef = useRef<Blob[]>([])\n  const streamRef = useRef<MediaStream | null>(null)\n  const timerRef = useRef<NodeJS.Timeout | null>(null)\n\n  useEffect(() => {\n    if (sessionId && session) {\n      startInterview()\n    }\n  }, [sessionId, session])\n\n  useEffect(() => {\n    return () => {\n      // Cleanup\n      if (timerRef.current) {\n        clearInterval(timerRef.current)\n      }\n      if (streamRef.current) {\n        streamRef.current.getTracks().forEach(track => track.stop())\n      }\n    }\n  }, [])\n\n  const startInterview = async () => {\n    try {\n      setIsLoading(true)\n      \n      // Start the interview session\n      const response = await fetch(`/api/mock-interview/${sessionId}/start`, {\n        method: 'POST',\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to start interview')\n      }\n\n      const data = await response.json()\n      setCurrentQuestion(data.firstQuestion)\n      setTotalQuestions(data.config.questionCount)\n      setTimeRemaining(data.config.duration * 60) // Convert to seconds\n      setSessionStarted(true)\n\n      // Setup camera and microphone\n      await setupMedia()\n      \n      // Start timer\n      startTimer()\n\n    } catch (error) {\n      console.error('Error starting interview:', error)\n      alert('Failed to start interview. Please try again.')\n      router.push('/mock-interview')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const setupMedia = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true\n      })\n      \n      streamRef.current = stream\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = stream\n      }\n    } catch (error) {\n      console.error('Error accessing media devices:', error)\n      alert('Please allow camera and microphone access for the interview.')\n    }\n  }\n\n  const startTimer = () => {\n    timerRef.current = setInterval(() => {\n      setTimeRemaining(prev => {\n        if (prev <= 1) {\n          // Time's up\n          handleTimeUp()\n          return 0\n        }\n        return prev - 1\n      })\n    }, 1000)\n  }\n\n  const handleTimeUp = () => {\n    if (timerRef.current) {\n      clearInterval(timerRef.current)\n    }\n    alert('Interview time is up!')\n    completeInterview()\n  }\n\n  const startRecording = () => {\n    if (!streamRef.current) return\n\n    recordedChunksRef.current = []\n    \n    const mediaRecorder = new MediaRecorder(streamRef.current)\n    mediaRecorderRef.current = mediaRecorder\n\n    mediaRecorder.ondataavailable = (event) => {\n      if (event.data.size > 0) {\n        recordedChunksRef.current.push(event.data)\n      }\n    }\n\n    mediaRecorder.start()\n    setIsRecording(true)\n  }\n\n  const stopRecording = () => {\n    if (mediaRecorderRef.current && isRecording) {\n      mediaRecorderRef.current.stop()\n      setIsRecording(false)\n    }\n  }\n\n  const submitAnswer = async () => {\n    if (!currentQuestion || !answer.trim()) {\n      alert('Please provide an answer before submitting.')\n      return\n    }\n\n    setIsSubmitting(true)\n    \n    try {\n      // Stop recording if active\n      if (isRecording) {\n        stopRecording()\n      }\n\n      // Submit answer\n      const response = await fetch(`/api/mock-interview/${sessionId}/answer`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          questionId: currentQuestion.id,\n          answer: answer.trim(),\n          responseTime: 60, // You can calculate actual response time\n        }),\n      })\n\n      if (!response.ok) {\n        throw new Error('Failed to submit answer')\n      }\n\n      const data = await response.json()\n      \n      // Show evaluation\n      setEvaluation(data.evaluation)\n      \n      // Check if interview is complete\n      if (data.isComplete) {\n        setIsComplete(true)\n      } else if (data.nextQuestion) {\n        // Prepare for next question\n        setTimeout(() => {\n          setCurrentQuestion(data.nextQuestion)\n          setQuestionNumber(prev => prev + 1)\n          setAnswer('')\n          setEvaluation(null)\n        }, 5000) // Show feedback for 5 seconds\n      }\n\n    } catch (error) {\n      console.error('Error submitting answer:', error)\n      alert('Failed to submit answer. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const completeInterview = () => {\n    if (timerRef.current) {\n      clearInterval(timerRef.current)\n    }\n    router.push(`/mock-interview/${sessionId}/results`)\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-lg text-gray-600\">Starting your interview...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (isComplete) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n          <div className=\"text-green-600 text-6xl mb-4\">🎉</div>\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Interview Complete!\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            Great job! Your interview has been completed. Click below to view your detailed results.\n          </p>\n          <button\n            onClick={completeInterview}\n            className=\"bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 font-semibold\"\n          >\n            View Results\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center\">\n              <VideoCameraIcon className=\"h-8 w-8 text-indigo-600 mr-3\" />\n              <h1 className=\"text-xl font-bold text-gray-900\">Mock Interview Session</h1>\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"flex items-center text-gray-600\">\n                <ClockIcon className=\"h-5 w-5 mr-2\" />\n                <span className=\"font-mono text-lg\">{formatTime(timeRemaining)}</span>\n              </div>\n              <div className=\"text-sm text-gray-500\">\n                Question {questionNumber} of {totalQuestions}\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Video Section */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Video Recording</h3>\n            <div className=\"relative\">\n              <video\n                ref={videoRef}\n                autoPlay\n                muted\n                className=\"w-full h-64 bg-gray-900 rounded-lg\"\n              />\n              {isRecording && (\n                <div className=\"absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center\">\n                  <div className=\"w-2 h-2 bg-white rounded-full mr-2 animate-pulse\"></div>\n                  Recording\n                </div>\n              )}\n            </div>\n            <div className=\"mt-4 flex justify-center space-x-4\">\n              {!isRecording ? (\n                <button\n                  onClick={startRecording}\n                  className=\"flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                >\n                  <MicrophoneIcon className=\"h-5 w-5 mr-2\" />\n                  Start Recording\n                </button>\n              ) : (\n                <button\n                  onClick={stopRecording}\n                  className=\"flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\"\n                >\n                  <StopIcon className=\"h-5 w-5 mr-2\" />\n                  Stop Recording\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Question and Answer Section */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            {!evaluation ? (\n              <>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Current Question</h3>\n                {currentQuestion && (\n                  <div className=\"mb-6\">\n                    <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4 mb-4\">\n                      <p className=\"text-blue-800 font-medium\">{currentQuestion.question}</p>\n                      <p className=\"text-blue-600 text-sm mt-2\">\n                        Type: {currentQuestion.type} | Category: {currentQuestion.category}\n                      </p>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"mb-6\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Your Answer\n                  </label>\n                  <textarea\n                    value={answer}\n                    onChange={(e) => setAnswer(e.target.value)}\n                    rows={8}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                    placeholder=\"Type your answer here...\"\n                  />\n                </div>\n\n                <button\n                  onClick={submitAnswer}\n                  disabled={isSubmitting || !answer.trim()}\n                  className=\"w-full flex items-center justify-center px-4 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSubmitting ? (\n                    <>\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"></div>\n                      Submitting...\n                    </>\n                  ) : (\n                    <>\n                      <PlayIcon className=\"h-5 w-5 mr-2\" />\n                      Submit Answer\n                    </>\n                  )}\n                </button>\n              </>\n            ) : (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Feedback</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                    <div className=\"flex items-center mb-2\">\n                      <span className=\"text-green-800 font-semibold\">Score: {evaluation.score}/10</span>\n                    </div>\n                    <p className=\"text-green-700\">{evaluation.feedback}</p>\n                  </div>\n                  \n                  {evaluation.keyPoints.length > 0 && (\n                    <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-blue-800 mb-2\">Key Points Mentioned:</h4>\n                      <ul className=\"list-disc list-inside text-blue-700\">\n                        {evaluation.keyPoints.map((point, index) => (\n                          <li key={index}>{point}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                  \n                  {evaluation.suggestions.length > 0 && (\n                    <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                      <h4 className=\"font-semibold text-yellow-800 mb-2\">Suggestions for Improvement:</h4>\n                      <ul className=\"list-disc list-inside text-yellow-700\">\n                        {evaluation.suggestions.map((suggestion, index) => (\n                          <li key={index}>{suggestion}</li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n                <p className=\"text-center text-gray-600 mt-4\">\n                  Preparing next question...\n                </p>\n              </div>\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AA6Be,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,SAAS;IAElC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAC3C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAC7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,aAAa,SAAS;gBACxB;YACF;QACF;yCAAG;QAAC;QAAW;KAAQ;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;kDAAO;oBACL,UAAU;oBACV,IAAI,SAAS,OAAO,EAAE;wBACpB,cAAc,SAAS,OAAO;oBAChC;oBACA,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO;8DAAC,CAAA,QAAS,MAAM,IAAI;;oBAC3D;gBACF;;QACF;yCAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,aAAa;YAEb,8BAA8B;YAC9B,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,UAAU,MAAM,CAAC,EAAE;gBACrE,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,mBAAmB,KAAK,aAAa;YACrC,kBAAkB,KAAK,MAAM,CAAC,aAAa;YAC3C,iBAAiB,KAAK,MAAM,CAAC,QAAQ,GAAG,IAAI,qBAAqB;;YACjE,kBAAkB;YAElB,8BAA8B;YAC9B,MAAM;YAEN,cAAc;YACd;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;YACN,OAAO,IAAI,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBACvD,OAAO;gBACP,OAAO;YACT;YAEA,UAAU,OAAO,GAAG;YAEpB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,SAAS,GAAG;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,MAAM,aAAa;QACjB,SAAS,OAAO,GAAG,YAAY;YAC7B,iBAAiB,CAAA;gBACf,IAAI,QAAQ,GAAG;oBACb,YAAY;oBACZ;oBACA,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;IACL;IAEA,MAAM,eAAe;QACnB,IAAI,SAAS,OAAO,EAAE;YACpB,cAAc,SAAS,OAAO;QAChC;QACA,MAAM;QACN;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,kBAAkB,OAAO,GAAG,EAAE;QAE9B,MAAM,gBAAgB,IAAI,cAAc,UAAU,OAAO;QACzD,iBAAiB,OAAO,GAAG;QAE3B,cAAc,eAAe,GAAG,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;gBACvB,kBAAkB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;YAC3C;QACF;QAEA,cAAc,KAAK;QACnB,eAAe;IACjB;IAEA,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,mBAAmB,CAAC,OAAO,IAAI,IAAI;YACtC,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B,IAAI,aAAa;gBACf;YACF;YAEA,gBAAgB;YAChB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,UAAU,OAAO,CAAC,EAAE;gBACtE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,YAAY,gBAAgB,EAAE;oBAC9B,QAAQ,OAAO,IAAI;oBACnB,cAAc;gBAChB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,kBAAkB;YAClB,cAAc,KAAK,UAAU;YAE7B,iCAAiC;YACjC,IAAI,KAAK,UAAU,EAAE;gBACnB,cAAc;YAChB,OAAO,IAAI,KAAK,YAAY,EAAE;gBAC5B,4BAA4B;gBAC5B,WAAW;oBACT,mBAAmB,KAAK,YAAY;oBACpC,kBAAkB,CAAA,OAAQ,OAAO;oBACjC,UAAU;oBACV,cAAc;gBAChB,GAAG,MAAM,8BAA8B;;YACzC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,SAAS,OAAO,EAAE;YACpB,cAAc,SAAS,OAAO;QAChC;QACA,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,UAAU,QAAQ,CAAC;IACpD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,YAAY;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;kCAC9C,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gOAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;;;;;;;0CAElD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAqB,WAAW;;;;;;;;;;;;kDAElD,6LAAC;wCAAI,WAAU;;4CAAwB;4CAC3B;4CAAe;4CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK;4CACL,QAAQ;4CACR,KAAK;4CACL,WAAU;;;;;;wCAEX,6BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;gDAAyD;;;;;;;;;;;;;8CAK9E,6LAAC;oCAAI,WAAU;8CACZ,CAAC,4BACA,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,8NAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;4CAAiB;;;;;;6DAI7C,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;sCAQ7C,6LAAC;4BAAI,WAAU;sCACZ,CAAC,2BACA;;kDACE,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;oCACxD,iCACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B,gBAAgB,QAAQ;;;;;;8DAClE,6LAAC;oDAAE,WAAU;;wDAA6B;wDACjC,gBAAgB,IAAI;wDAAC;wDAAc,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;kDAM1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCACC,SAAS;wCACT,UAAU,gBAAgB,CAAC,OAAO,IAAI;wCACtC,WAAU;kDAET,6BACC;;8DACE,6LAAC;oDAAI,WAAU;;;;;;gDAAuE;;yEAIxF;;8DACE,6LAAC,kNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;6DAO7C,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEAA+B;gEAAQ,WAAW,KAAK;gEAAC;;;;;;;;;;;;kEAE1E,6LAAC;wDAAE,WAAU;kEAAkB,WAAW,QAAQ;;;;;;;;;;;;4CAGnD,WAAW,SAAS,CAAC,MAAM,GAAG,mBAC7B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAG,WAAU;kEACX,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChC,6LAAC;0EAAgB;+DAAR;;;;;;;;;;;;;;;;4CAMhB,WAAW,WAAW,CAAC,MAAM,GAAG,mBAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,6LAAC;wDAAG,WAAU;kEACX,WAAW,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBACvC,6LAAC;0EAAgB;+DAAR;;;;;;;;;;;;;;;;;;;;;;kDAMnB,6LAAC;wCAAE,WAAU;kDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D;GA5YwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/%40heroicons/react/24/outline/esm/VideoCameraIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction VideoCameraIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(VideoCameraIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/%40heroicons/react/24/outline/esm/MicrophoneIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MicrophoneIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MicrophoneIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,eAAe,EACtB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/%40heroicons/react/24/outline/esm/StopIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction StopIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(StopIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/%40heroicons/react/24/outline/esm/PlayIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction PlayIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(PlayIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/%40heroicons/react/24/outline/esm/ClockIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}