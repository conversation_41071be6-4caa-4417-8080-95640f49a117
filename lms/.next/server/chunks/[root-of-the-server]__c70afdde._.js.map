{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE5D,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { db } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await db.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            studentProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          avatar: user.avatar,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/api/mock-interview/create/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { db } from '@/lib/db'\nimport { InterviewType, DifficultyLevel, InterviewStatus } from '@prisma/client'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    const {\n      interviewType,\n      domain,\n      difficulty,\n      duration,\n      aiPersonality,\n      customPrompt\n    } = await request.json()\n\n    // Validate required fields\n    if (!interviewType || !domain || !difficulty || !duration) {\n      return NextResponse.json(\n        { error: 'Missing required fields' },\n        { status: 400 }\n      )\n    }\n\n    // Create mock interview session\n    const session_data = await db.mockInterviewSession.create({\n      data: {\n        userId: session.user.id,\n        interviewType: interviewType as InterviewType,\n        domain,\n        difficulty: difficulty as DifficultyLevel,\n        duration: parseInt(duration),\n        status: InterviewStatus.SCHEDULED,\n        aiPersonality,\n        customPrompt,\n        scheduledAt: new Date(),\n      },\n      include: {\n        user: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            studentProfile: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Mock interview session created successfully',\n      session: session_data\n    })\n\n  } catch (error) {\n    console.error('Error creating mock interview session:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EACJ,aAAa,EACb,MAAM,EACN,UAAU,EACV,QAAQ,EACR,aAAa,EACb,YAAY,EACb,GAAG,MAAM,QAAQ,IAAI;QAEtB,2BAA2B;QAC3B,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,eAAe,MAAM,kHAAA,CAAA,KAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC;YACxD,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,eAAe;gBACf;gBACA,YAAY;gBACZ,UAAU,SAAS;gBACnB,QAAQ,6HAAA,CAAA,kBAAe,CAAC,SAAS;gBACjC;gBACA;gBACA,aAAa,IAAI;YACnB;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,gBAAgB;oBAClB;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}