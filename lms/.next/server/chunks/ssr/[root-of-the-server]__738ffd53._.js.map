{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/mock-interview/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport {\n  VideoCameraIcon,\n  ClockIcon,\n  AcademicCapIcon,\n  ChartBarIcon,\n  PlayIcon\n} from '@heroicons/react/24/outline'\n\nexport default function MockInterviewPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [isCreating, setIsCreating] = useState(false)\n  const [formData, setFormData] = useState({\n    interviewType: 'TECHNICAL',\n    domain: 'Software Engineering',\n    difficulty: 'MEDIUM',\n    duration: 30,\n    aiPersonality: 'professional',\n    customPrompt: ''\n  })\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center\">\n          <VideoCameraIcon className=\"h-16 w-16 text-indigo-600 mx-auto mb-4\" />\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            AI Mock Interview\n          </h2>\n          <p className=\"text-gray-600 mb-6\">\n            Sign in to start practicing with our AI-powered interview system\n          </p>\n          <Link\n            href=\"/auth/signin\"\n            className=\"bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 font-semibold\"\n          >\n            Sign In to Continue\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  const handleCreateInterview = async () => {\n    setIsCreating(true)\n    try {\n      const response = await fetch('/api/mock-interview/create', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        router.push(`/mock-interview/${data.session.id}`)\n      } else {\n        const error = await response.json()\n        alert(error.error || 'Failed to create interview session')\n      }\n    } catch (error) {\n      console.error('Error creating interview:', error)\n      alert('An error occurred. Please try again.')\n    } finally {\n      setIsCreating(false)\n    }\n  }\n\n  const interviewTypes = [\n    { value: 'TECHNICAL', label: 'Technical Interview', description: 'Coding, algorithms, system design' },\n    { value: 'BEHAVIORAL', label: 'Behavioral Interview', description: 'Leadership, teamwork, problem-solving' },\n    { value: 'HR', label: 'HR Interview', description: 'General questions, company fit' },\n    { value: 'MIXED', label: 'Mixed Interview', description: 'Combination of technical and behavioral' }\n  ]\n\n  const domains = [\n    'Software Engineering',\n    'Data Science',\n    'Product Management',\n    'DevOps',\n    'Frontend Development',\n    'Backend Development',\n    'Mobile Development',\n    'Machine Learning',\n    'Cybersecurity',\n    'Cloud Architecture'\n  ]\n\n  const difficulties = [\n    { value: 'EASY', label: 'Easy', description: 'Entry level questions' },\n    { value: 'MEDIUM', label: 'Medium', description: 'Mid-level questions' },\n    { value: 'HARD', label: 'Hard', description: 'Senior level questions' },\n    { value: 'EXPERT', label: 'Expert', description: 'Principal/Staff level questions' }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-indigo-600 hover:text-indigo-800 mr-4\">\n                ← Back to Dashboard\n              </Link>\n              <VideoCameraIcon className=\"h-8 w-8 text-indigo-600 mr-3\" />\n              <h1 className=\"text-2xl font-bold text-gray-900\">AI Mock Interview</h1>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Practice with AI-Powered Interviews\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Get instant feedback, improve your skills, and boost your confidence with our advanced AI interviewer\n          </p>\n        </div>\n\n        {/* Features */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\n          <div className=\"bg-white rounded-lg shadow p-6 text-center\">\n            <ClockIcon className=\"h-12 w-12 text-blue-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Real-time Feedback</h3>\n            <p className=\"text-gray-600\">Get instant evaluation and suggestions after each answer</p>\n          </div>\n          <div className=\"bg-white rounded-lg shadow p-6 text-center\">\n            <AcademicCapIcon className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Adaptive Questions</h3>\n            <p className=\"text-gray-600\">AI generates questions based on your experience and role</p>\n          </div>\n          <div className=\"bg-white rounded-lg shadow p-6 text-center\">\n            <ChartBarIcon className=\"h-12 w-12 text-purple-600 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Detailed Analytics</h3>\n            <p className=\"text-gray-600\">Track your progress and identify areas for improvement</p>\n          </div>\n        </div>\n\n        {/* Interview Configuration */}\n        <div className=\"bg-white rounded-lg shadow-lg p-8\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Configure Your Interview</h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Interview Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Interview Type\n              </label>\n              <div className=\"space-y-3\">\n                {interviewTypes.map((type) => (\n                  <label key={type.value} className=\"flex items-start\">\n                    <input\n                      type=\"radio\"\n                      name=\"interviewType\"\n                      value={type.value}\n                      checked={formData.interviewType === type.value}\n                      onChange={(e) => setFormData({ ...formData, interviewType: e.target.value })}\n                      className=\"mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300\"\n                    />\n                    <div className=\"ml-3\">\n                      <div className=\"text-sm font-medium text-gray-900\">{type.label}</div>\n                      <div className=\"text-sm text-gray-500\">{type.description}</div>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Domain */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Domain/Field\n              </label>\n              <select\n                value={formData.domain}\n                onChange={(e) => setFormData({ ...formData, domain: e.target.value })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n              >\n                {domains.map((domain) => (\n                  <option key={domain} value={domain}>{domain}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Difficulty */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Difficulty Level\n              </label>\n              <div className=\"space-y-3\">\n                {difficulties.map((diff) => (\n                  <label key={diff.value} className=\"flex items-start\">\n                    <input\n                      type=\"radio\"\n                      name=\"difficulty\"\n                      value={diff.value}\n                      checked={formData.difficulty === diff.value}\n                      onChange={(e) => setFormData({ ...formData, difficulty: e.target.value })}\n                      className=\"mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300\"\n                    />\n                    <div className=\"ml-3\">\n                      <div className=\"text-sm font-medium text-gray-900\">{diff.label}</div>\n                      <div className=\"text-sm text-gray-500\">{diff.description}</div>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Duration */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                Duration (minutes)\n              </label>\n              <select\n                value={formData.duration}\n                onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n              >\n                <option value={15}>15 minutes (3-4 questions)</option>\n                <option value={30}>30 minutes (6-7 questions)</option>\n                <option value={45}>45 minutes (9-10 questions)</option>\n                <option value={60}>60 minutes (12-13 questions)</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Start Interview Button */}\n          <div className=\"mt-8 text-center\">\n            <button\n              onClick={handleCreateInterview}\n              disabled={isCreating}\n              className=\"inline-flex items-center px-8 py-4 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isCreating ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3\"></div>\n                  Creating Interview...\n                </>\n              ) : (\n                <>\n                  <PlayIcon className=\"h-5 w-5 mr-3\" />\n                  Start Mock Interview\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAce,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,eAAe;QACf,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,eAAe;QACf,cAAc;IAChB;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6NAAA,CAAA,kBAAe;wBAAC,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,wBAAwB;QAC5B,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE;YAClD,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,MAAM,KAAK,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAa,OAAO;YAAuB,aAAa;QAAoC;QACrG;YAAE,OAAO;YAAc,OAAO;YAAwB,aAAa;QAAwC;QAC3G;YAAE,OAAO;YAAM,OAAO;YAAgB,aAAa;QAAiC;QACpF;YAAE,OAAO;YAAS,OAAO;YAAmB,aAAa;QAA0C;KACpG;IAED,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe;QACnB;YAAE,OAAO;YAAQ,OAAO;YAAQ,aAAa;QAAwB;QACrE;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAAsB;QACvE;YAAE,OAAO;YAAQ,OAAO;YAAQ,aAAa;QAAyB;QACtE;YAAE,OAAO;YAAU,OAAO;YAAU,aAAa;QAAkC;KACpF;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAA6C;;;;;;8CAG/E,8OAAC,6NAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,uNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;0DACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,KAAK,KAAK;gEACjB,SAAS,SAAS,aAAa,KAAK,KAAK,KAAK;gEAC9C,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAC1E,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAqC,KAAK,KAAK;;;;;;kFAC9D,8OAAC;wEAAI,WAAU;kFAAyB,KAAK,WAAW;;;;;;;;;;;;;uDAXhD,KAAK,KAAK;;;;;;;;;;;;;;;;kDAmB5B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACnE,WAAU;0DAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;;;;;;;kDAMnB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;wDAAuB,WAAU;;0EAChC,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,KAAK,KAAK;gEACjB,SAAS,SAAS,UAAU,KAAK,KAAK,KAAK;gEAC3C,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACvE,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAqC,KAAK,KAAK;;;;;;kFAC9D,8OAAC;wEAAI,WAAU;kFAAyB,KAAK,WAAW;;;;;;;;;;;;;uDAXhD,KAAK,KAAK;;;;;;;;;;;;;;;;kDAmB5B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAE;gDAC/E,WAAU;;kEAEV,8OAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;kEACnB,8OAAC;wDAAO,OAAO;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,2BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAuE;;qEAIxF;;0DACE,8OAAC,+MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvD", "debugId": null}}]}