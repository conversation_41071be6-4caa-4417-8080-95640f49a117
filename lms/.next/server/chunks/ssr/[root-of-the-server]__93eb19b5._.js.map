{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport Link from 'next/link'\nimport { \n  VideoIcon, \n  AcademicCapIcon,\n  UserGroupIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline'\n\nexport default function Home() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'authenticated') {\n      router.push('/dashboard')\n    }\n  }, [status, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (session) {\n    return null // Will redirect to dashboard\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <AcademicCapIcon className=\"h-8 w-8 text-indigo-600 mr-3\" />\n              <h1 className=\"text-2xl font-bold text-gray-900\">GraphyLMS</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/auth/signin\"\n                className=\"text-gray-600 hover:text-gray-900 font-medium\"\n              >\n                Sign In\n              </Link>\n              <Link\n                href=\"/auth/signup\"\n                className=\"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 font-medium\"\n              >\n                Get Started\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Master Your Skills with\n            <span className=\"text-indigo-600\"> AI-Powered</span> Learning\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Experience the future of education with our comprehensive LMS featuring \n            AI mock interviews, personalized learning paths, and real-time progress tracking.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/auth/signup\"\n              className=\"bg-indigo-600 text-white px-8 py-4 rounded-lg hover:bg-indigo-700 font-semibold text-lg\"\n            >\n              Start Learning Today\n            </Link>\n            <Link\n              href=\"/mock-interview\"\n              className=\"border border-indigo-600 text-indigo-600 px-8 py-4 rounded-lg hover:bg-indigo-50 font-semibold text-lg\"\n            >\n              Try Mock Interview\n            </Link>\n          </div>\n        </div>\n\n        {/* Features */}\n        <div className=\"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <div className=\"text-center p-6\">\n            <div className=\"bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <VideoIcon className=\"h-8 w-8 text-indigo-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">AI Mock Interviews</h3>\n            <p className=\"text-gray-600\">\n              Practice with our AI interviewer and get instant feedback on your performance\n            </p>\n          </div>\n          \n          <div className=\"text-center p-6\">\n            <div className=\"bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <AcademicCapIcon className=\"h-8 w-8 text-green-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Course Management</h3>\n            <p className=\"text-gray-600\">\n              Access comprehensive courses with video content, assignments, and assessments\n            </p>\n          </div>\n          \n          <div className=\"text-center p-6\">\n            <div className=\"bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <ChartBarIcon className=\"h-8 w-8 text-purple-600\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Progress Tracking</h3>\n            <p className=\"text-gray-600\">\n              Monitor your learning journey with detailed analytics and performance insights\n            </p>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AANA;;;;;;;AAae,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,SAAS;QACX,OAAO,KAAK,6BAA6B;;IAC3C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAC3B,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAoD;kDAEhE,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;oCAAkB;;;;;;;0CAEtD,8OAAC;gCAAE,WAAU;0CAA+C;;;;;;0CAI5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sLAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;;;;;;kDAE7B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAK/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}]}