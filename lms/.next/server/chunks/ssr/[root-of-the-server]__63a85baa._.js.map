{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport Link from 'next/link'\nimport {\n  VideoCameraIcon,\n  UserGroupIcon,\n  AcademicCapIcon,\n  ChartBarIcon,\n  PlayIcon,\n  BookOpenIcon\n} from '@heroicons/react/24/outline'\n\nexport default function DashboardPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/auth/signin')\n    }\n  }, [status, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"></div>\n      </div>\n    )\n  }\n\n  if (!session) {\n    return null\n  }\n\n  const quickActions = [\n    {\n      title: 'Start Mock Interview',\n      description: 'Practice with AI-powered interview sessions',\n      icon: VideoCameraIcon,\n      href: '/mock-interview',\n      color: 'bg-blue-500',\n      featured: true\n    },\n    {\n      title: 'Browse Courses',\n      description: 'Explore and enroll in courses',\n      icon: BookOpenIcon,\n      href: '/courses',\n      color: 'bg-green-500'\n    },\n    ...(session?.user?.role === 'INSTRUCTOR' ? [{\n      title: 'Create Course',\n      description: 'Create and manage your courses',\n      icon: AcademicCapIcon,\n      href: '/courses/create',\n      color: 'bg-indigo-500'\n    }] : []),\n    {\n      title: 'Profile Settings',\n      description: 'Update your profile and preferences',\n      icon: UserGroupIcon,\n      href: '/profile',\n      color: 'bg-purple-500'\n    },\n    {\n      title: 'Progress Analytics',\n      description: 'View your learning progress',\n      icon: ChartBarIcon,\n      href: '/analytics',\n      color: 'bg-orange-500'\n    }\n  ]\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">\n                Welcome back, {session.user.name}!\n              </h1>\n              <p className=\"text-gray-600\">Ready to continue your learning journey?</p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800\">\n                {session.user.role}\n              </span>\n              <button\n                onClick={() => router.push('/auth/signout')}\n                className=\"text-gray-500 hover:text-gray-700\"\n              >\n                Sign out\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Featured Action */}\n        <div className=\"mb-8\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg p-8 text-white\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h2 className=\"text-2xl font-bold mb-2\">🎯 Ready for your next interview?</h2>\n                <p className=\"text-blue-100 mb-4\">\n                  Practice with our AI-powered mock interview system and get instant feedback\n                </p>\n                <Link\n                  href=\"/mock-interview\"\n                  className=\"inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors\"\n                >\n                  <PlayIcon className=\"h-5 w-5 mr-2\" />\n                  Start Mock Interview\n                </Link>\n              </div>\n              <div className=\"hidden md:block\">\n                <VideoIcon className=\"h-24 w-24 text-blue-200\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {quickActions.map((action) => (\n            <Link\n              key={action.title}\n              href={action.href}\n              className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group\"\n            >\n              <div className=\"flex items-center mb-4\">\n                <div className={`${action.color} p-3 rounded-lg`}>\n                  <action.icon className=\"h-6 w-6 text-white\" />\n                </div>\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600\">\n                {action.title}\n              </h3>\n              <p className=\"text-gray-600 text-sm\">{action.description}</p>\n            </Link>\n          ))}\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Recent Activity</h3>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"text-center py-8\">\n              <AcademicCapIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500\">No recent activity yet.</p>\n              <p className=\"text-gray-400 text-sm mt-2\">\n                Start a mock interview or enroll in a course to see your activity here.\n              </p>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,eAAe;QACnB;YACE,OAAO;YACP,aAAa;YACb,MAAM,6NAAA,CAAA,kBAAe;YACrB,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,uNAAA,CAAA,eAAY;YAClB,MAAM;YACN,OAAO;QACT;WACI,SAAS,MAAM,SAAS,eAAe;YAAC;gBAC1C,OAAO;gBACP,aAAa;gBACb,MAAM,6NAAA,CAAA,kBAAe;gBACrB,MAAM;gBACN,OAAO;YACT;SAAE,GAAG,EAAE;QACP;YACE,OAAO;YACP,aAAa;YACb,MAAM,yNAAA,CAAA,gBAAa;YACnB,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,uNAAA,CAAA,eAAY;YAClB,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAAmC;4CAChC,QAAQ,IAAI,CAAC,IAAI;4CAAC;;;;;;;kDAEnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,QAAQ,IAAI,CAAC,IAAI;;;;;;kDAEpB,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI,CAAC;wCAC3B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7B,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,OAAO,IAAI;gCACjB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,GAAG,OAAO,KAAK,CAAC,eAAe,CAAC;sDAC9C,cAAA,8OAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG3B,8OAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;kDAEf,8OAAC;wCAAE,WAAU;kDAAyB,OAAO,WAAW;;;;;;;+BAZnD,OAAO,KAAK;;;;;;;;;;kCAkBvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;sDAC3B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD", "debugId": null}}]}