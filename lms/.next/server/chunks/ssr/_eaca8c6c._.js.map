{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/dashboard/page.tsx"], "sourcesContent": ["export default function DashboardPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">\n                Welcome to GraphyLMS Dashboard!\n              </h1>\n              <p className=\"text-gray-600\">Your learning journey starts here</p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800\">\n                Demo User\n              </span>\n              <a href=\"/\" className=\"text-gray-500 hover:text-gray-700\">\n                ← Back to Home\n              </a>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Featured Action */}\n        <div className=\"mb-8\">\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg p-8 text-white\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h2 className=\"text-2xl font-bold mb-2\">🎯 Ready for your next interview?</h2>\n                <p className=\"text-blue-100 mb-4\">\n                  Practice with our AI-powered mock interview system and get instant feedback\n                </p>\n                <a\n                  href=\"/mock-interview\"\n                  className=\"inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors\"\n                >\n                  <span className=\"mr-2\">▶️</span>\n                  Start Mock Interview\n                </a>\n              </div>\n              <div className=\"hidden md:block\">\n                <span className=\"text-6xl\">🎥</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <a\n            href=\"/mock-interview\"\n            className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"bg-blue-500 p-3 rounded-lg\">\n                <span className=\"text-white text-xl\">🤖</span>\n              </div>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600\">\n              Start Mock Interview\n            </h3>\n            <p className=\"text-gray-600 text-sm\">Practice with AI-powered interview sessions</p>\n          </a>\n\n          <a\n            href=\"/courses\"\n            className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"bg-green-500 p-3 rounded-lg\">\n                <span className=\"text-white text-xl\">📚</span>\n              </div>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600\">\n              Browse Courses\n            </h3>\n            <p className=\"text-gray-600 text-sm\">Explore and enroll in courses</p>\n          </a>\n\n          <a\n            href=\"/courses/create\"\n            className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"bg-indigo-500 p-3 rounded-lg\">\n                <span className=\"text-white text-xl\">🎓</span>\n              </div>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600\">\n              Create Course\n            </h3>\n            <p className=\"text-gray-600 text-sm\">Create and manage your courses</p>\n          </a>\n\n          <a\n            href=\"/profile\"\n            className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group\"\n          >\n            <div className=\"flex items-center mb-4\">\n              <div className=\"bg-purple-500 p-3 rounded-lg\">\n                <span className=\"text-white text-xl\">👤</span>\n              </div>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600\">\n              Profile Settings\n            </h3>\n            <p className=\"text-gray-600 text-sm\">Update your profile and preferences</p>\n          </a>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📊</span>\n              </div>\n              <div className=\"ml-4\">\n                <div className=\"text-2xl font-bold text-gray-900\">5</div>\n                <div className=\"text-gray-600\">Courses Available</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">🎯</span>\n              </div>\n              <div className=\"ml-4\">\n                <div className=\"text-2xl font-bold text-gray-900\">12</div>\n                <div className=\"text-gray-600\">Mock Interviews</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">⭐</span>\n              </div>\n              <div className=\"ml-4\">\n                <div className=\"text-2xl font-bold text-gray-900\">4.8</div>\n                <div className=\"text-gray-600\">Average Rating</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">🚀 Available Features</h3>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center p-4 bg-green-50 rounded-lg\">\n                <span className=\"text-green-600 text-xl mr-4\">✅</span>\n                <div>\n                  <div className=\"font-medium text-gray-900\">AI Mock Interview System</div>\n                  <div className=\"text-sm text-gray-600\">Real-time AI interviewer with video recording and feedback</div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center p-4 bg-green-50 rounded-lg\">\n                <span className=\"text-green-600 text-xl mr-4\">✅</span>\n                <div>\n                  <div className=\"font-medium text-gray-900\">Course Management</div>\n                  <div className=\"text-sm text-gray-600\">Create, publish, and manage comprehensive courses</div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center p-4 bg-green-50 rounded-lg\">\n                <span className=\"text-green-600 text-xl mr-4\">✅</span>\n                <div>\n                  <div className=\"font-medium text-gray-900\">Student Enrollment</div>\n                  <div className=\"text-sm text-gray-600\">Browse and enroll in courses with progress tracking</div>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center p-4 bg-blue-50 rounded-lg\">\n                <span className=\"text-blue-600 text-xl mr-4\">🔧</span>\n                <div>\n                  <div className=\"font-medium text-gray-900\">Authentication System</div>\n                  <div className=\"text-sm text-gray-600\">User registration, login, and role-based access control</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Links */}\n        <div className=\"mt-8 text-center\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Navigation</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            <a href=\"/\" className=\"bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-gray-700\">\n              🏠 Home\n            </a>\n            <a href=\"/auth/signin\" className=\"bg-indigo-100 hover:bg-indigo-200 px-4 py-2 rounded-lg text-indigo-700\">\n              🔑 Sign In\n            </a>\n            <a href=\"/auth/signup\" className=\"bg-green-100 hover:bg-green-200 px-4 py-2 rounded-lg text-green-700\">\n              📝 Sign Up\n            </a>\n            <a href=\"/courses\" className=\"bg-blue-100 hover:bg-blue-200 px-4 py-2 rounded-lg text-blue-700\">\n              📚 Courses\n            </a>\n            <a href=\"/mock-interview\" className=\"bg-purple-100 hover:bg-purple-200 px-4 py-2 rounded-lg text-purple-700\">\n              🎯 Mock Interview\n            </a>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAoG;;;;;;kDAGpH,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlE,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,8OAAC;gDACC,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAS;;;;;;;;;;;;;kDAIpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;kDAGzC,8OAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;8DAC9C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAI3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;8DAC9C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAI3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;8DAC9C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAI3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA4B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAmE;;;;;;kDAGzF,8OAAC;wCAAE,MAAK;wCAAe,WAAU;kDAAyE;;;;;;kDAG1G,8OAAC;wCAAE,MAAK;wCAAe,WAAU;kDAAsE;;;;;;kDAGvG,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAmE;;;;;;kDAGhG,8OAAC;wCAAE,MAAK;wCAAkB,WAAU;kDAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzH", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,EAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}