{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function SignUpPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: 'STUDENT'\n  })\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match')\n      setIsLoading(false)\n      return\n    }\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          email: formData.email,\n          password: formData.password,\n          role: formData.role,\n        }),\n      })\n\n      if (response.ok) {\n        router.push('/auth/signin?message=Registration successful')\n      } else {\n        const data = await response.json()\n        setError(data.error || 'Registration failed')\n      }\n    } catch (error) {\n      setError('An error occurred. Please try again.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Join our LMS platform and start your learning journey\n          </p>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n              {error}\n            </div>\n          )}\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Full Name\n              </label>\n              <input\n                id=\"name\"\n                name=\"name\"\n                type=\"text\"\n                required\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your full name\"\n                value={formData.name}\n                onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email Address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                required\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your email\"\n                value={formData.email}\n                onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700\">\n                Role\n              </label>\n              <select\n                id=\"role\"\n                name=\"role\"\n                className=\"mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                value={formData.role}\n                onChange={(e) => setFormData({ ...formData, role: e.target.value })}\n              >\n                <option value=\"STUDENT\">Student</option>\n                <option value=\"INSTRUCTOR\">Instructor</option>\n              </select>\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                required\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your password\"\n                value={formData.password}\n                onChange={(e) => setFormData({ ...formData, password: e.target.value })}\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                required\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Confirm your password\"\n                value={formData.confirmPassword}\n                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}\n              />\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50\"\n            >\n              {isLoading ? 'Creating Account...' : 'Create Account'}\n            </button>\n          </div>\n          \n          <div className=\"text-center\">\n            <span className=\"text-sm text-gray-600\">\n              Already have an account?{' '}\n              <Link href=\"/auth/signin\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n                Sign in\n              </Link>\n            </span>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,MAAM;IACR;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,aAAa;YACb;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;gBACrB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;;;;;;;8BAIxD,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;wBACxC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAIrE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAItE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,WAAU;4CACV,OAAO,SAAS,IAAI;4CACpB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;;8DAEjE,8OAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,8OAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAIzE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,WAAU;4CACV,aAAY;4CACZ,OAAO,SAAS,eAAe;4CAC/B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;;;;;;;sCAKlF,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,YAAY,wBAAwB;;;;;;;;;;;sCAIzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;oCAAwB;oCACb;kDACzB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAe,WAAU;kDAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStG", "debugId": null}}]}