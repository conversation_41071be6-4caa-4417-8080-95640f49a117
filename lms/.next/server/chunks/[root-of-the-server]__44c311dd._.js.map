{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KAAK,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAE5D,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from 'next-auth/providers/credentials'\nimport bcrypt from 'bcryptjs'\nimport { db } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await db.user.findUnique({\n          where: {\n            email: credentials.email\n          },\n          include: {\n            studentProfile: true\n          }\n        })\n\n        if (!user) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.name,\n          role: user.role,\n          avatar: user.avatar,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.id = user.id\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.id as string\n        session.user.role = token.role as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    signUp: '/auth/signup'\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,kHAAA,CAAA,KAAE,CAAC,IAAI,CAAC,UAAU,CAAC;oBACpC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;oBACA,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,EAAE,GAAG,KAAK,EAAE;YACpB;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE;gBAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;YAChC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/graphyLMS/lms/src/app/api/courses/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { db } from '@/lib/db'\nimport { CourseLevel } from '@prisma/client'\n\n// GET /api/courses - Get all courses or user's courses\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    const { searchParams } = new URL(request.url)\n    const type = searchParams.get('type') // 'all', 'my-courses', 'created'\n    const category = searchParams.get('category')\n    const level = searchParams.get('level')\n    const search = searchParams.get('search')\n\n    let whereClause: any = {}\n\n    // Filter by published status for public courses\n    if (type !== 'created') {\n      whereClause.isPublished = true\n    }\n\n    // Filter by instructor's courses\n    if (type === 'created' && session?.user?.id) {\n      whereClause.instructorId = session.user.id\n    }\n\n    // Filter by enrolled courses\n    if (type === 'my-courses' && session?.user?.id) {\n      whereClause.enrollments = {\n        some: {\n          userId: session.user.id\n        }\n      }\n    }\n\n    // Additional filters\n    if (category) {\n      whereClause.category = category\n    }\n\n    if (level) {\n      whereClause.level = level as CourseLevel\n    }\n\n    if (search) {\n      whereClause.OR = [\n        { title: { contains: search, mode: 'insensitive' } },\n        { description: { contains: search, mode: 'insensitive' } }\n      ]\n    }\n\n    const courses = await db.course.findMany({\n      where: whereClause,\n      include: {\n        instructor: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true\n          }\n        },\n        enrollments: {\n          select: {\n            id: true,\n            userId: true\n          }\n        },\n        reviews: {\n          select: {\n            rating: true\n          }\n        },\n        _count: {\n          select: {\n            lessons: true,\n            enrollments: true\n          }\n        }\n      },\n      orderBy: {\n        createdAt: 'desc'\n      }\n    })\n\n    // Calculate average rating and enrollment count\n    const coursesWithStats = courses.map(course => ({\n      ...course,\n      averageRating: course.reviews.length > 0 \n        ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length \n        : 0,\n      enrollmentCount: course._count.enrollments,\n      lessonCount: course._count.lessons,\n      isEnrolled: session?.user?.id ? course.enrollments.some(e => e.userId === session.user.id) : false\n    }))\n\n    return NextResponse.json({\n      courses: coursesWithStats,\n      total: coursesWithStats.length\n    })\n\n  } catch (error) {\n    console.error('Error fetching courses:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/courses - Create a new course\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      )\n    }\n\n    // Check if user is instructor or admin\n    if (session.user.role !== 'INSTRUCTOR' && session.user.role !== 'ADMIN') {\n      return NextResponse.json(\n        { error: 'Only instructors can create courses' },\n        { status: 403 }\n      )\n    }\n\n    const {\n      title,\n      description,\n      category,\n      level,\n      price,\n      duration,\n      language,\n      thumbnail\n    } = await request.json()\n\n    // Validate required fields\n    if (!title) {\n      return NextResponse.json(\n        { error: 'Course title is required' },\n        { status: 400 }\n      )\n    }\n\n    // Create course\n    const course = await db.course.create({\n      data: {\n        title,\n        description,\n        category,\n        level: level as CourseLevel || 'BEGINNER',\n        price: price ? parseFloat(price) : null,\n        duration: duration ? parseInt(duration) : null,\n        language: language || 'English',\n        thumbnail,\n        instructorId: session.user.id,\n        isPublished: false\n      },\n      include: {\n        instructor: {\n          select: {\n            id: true,\n            name: true,\n            avatar: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json({\n      message: 'Course created successfully',\n      course\n    }, { status: 201 })\n\n  } catch (error) {\n    console.error('Error creating course:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC,QAAQ,iCAAiC;;QACvE,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,cAAmB,CAAC;QAExB,gDAAgD;QAChD,IAAI,SAAS,WAAW;YACtB,YAAY,WAAW,GAAG;QAC5B;QAEA,iCAAiC;QACjC,IAAI,SAAS,aAAa,SAAS,MAAM,IAAI;YAC3C,YAAY,YAAY,GAAG,QAAQ,IAAI,CAAC,EAAE;QAC5C;QAEA,6BAA6B;QAC7B,IAAI,SAAS,gBAAgB,SAAS,MAAM,IAAI;YAC9C,YAAY,WAAW,GAAG;gBACxB,MAAM;oBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACzB;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,UAAU;YACZ,YAAY,QAAQ,GAAG;QACzB;QAEA,IAAI,OAAO;YACT,YAAY,KAAK,GAAG;QACtB;QAEA,IAAI,QAAQ;YACV,YAAY,EAAE,GAAG;gBACf;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;gBACnD;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAc;gBAAE;aAC1D;QACH;QAEA,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvC,OAAO;YACP,SAAS;gBACP,YAAY;oBACV,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;gBACA,aAAa;oBACX,QAAQ;wBACN,IAAI;wBACJ,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,QAAQ;wBACN,QAAQ;oBACV;gBACF;gBACA,QAAQ;oBACN,QAAQ;wBACN,SAAS;wBACT,aAAa;oBACf;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,gDAAgD;QAChD,MAAM,mBAAmB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC9C,GAAG,MAAM;gBACT,eAAe,OAAO,OAAO,CAAC,MAAM,GAAG,IACnC,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM,GACtF;gBACJ,iBAAiB,OAAO,MAAM,CAAC,WAAW;gBAC1C,aAAa,OAAO,MAAM,CAAC,OAAO;gBAClC,YAAY,SAAS,MAAM,KAAK,OAAO,WAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,IAAI;YAC/F,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO,iBAAiB,MAAM;QAChC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,uCAAuC;QACvC,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,gBAAgB,QAAQ,IAAI,CAAC,IAAI,KAAK,SAAS;YACvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,SAAS,EACV,GAAG,MAAM,QAAQ,IAAI;QAEtB,2BAA2B;QAC3B,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,SAAS,MAAM,kHAAA,CAAA,KAAE,CAAC,MAAM,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA;gBACA,OAAO,SAAwB;gBAC/B,OAAO,QAAQ,WAAW,SAAS;gBACnC,UAAU,WAAW,SAAS,YAAY;gBAC1C,UAAU,YAAY;gBACtB;gBACA,cAAc,QAAQ,IAAI,CAAC,EAAE;gBAC7B,aAAa;YACf;YACA,SAAS;gBACP,YAAY;oBACV,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}