# 🎓 GraphyLMS - AI-Powered Learning Management System

A comprehensive Learning Management System with AI-powered mock interviews, course management, and student tracking.

## 🚀 Features

### ✅ **Core Features**
- **🔐 Authentication & User Management**: Role-based access (Student, Instructor, Admin)
- **🤖 AI Mock Interview System**: Real-time AI interviewer with video recording and feedback
- **📚 Course Management**: Create, publish, and manage courses with modules and lessons
- **👥 Student Enrollment**: Course enrollment and progress tracking
- **📊 Analytics Dashboard**: Comprehensive reporting and analytics
- **🎨 Modern UI/UX**: Responsive design with Tailwind CSS

### 🎯 **AI Mock Interview Features**
- Real-time AI question generation based on role and experience
- Video and audio recording capabilities
- Instant feedback and scoring (1-10 scale)
- Detailed performance analytics
- Improvement suggestions and recommendations
- Multiple interview types (Technical, Behavioral, HR, Mixed)

### 📖 **Course Management Features**
- Course creation with modules and lessons
- Video content support
- Student enrollment and progress tracking
- Course reviews and ratings
- Category and difficulty level filtering
- Free and paid course options

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React, <PERSON>Script, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: SQLite with Prisma ORM
- **Authentication**: NextAuth.js
- **AI Integration**: OpenAI GPT-4
- **Video Recording**: WebRTC, MediaRecorder API
- **Icons**: Heroicons

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Git installed

### Installation

1. **Navigate to the project**:
   ```bash
   cd /Users/<USER>/Documents/augment-projects/graphyLMS/lms
   ```

2. **Install dependencies** (already done):
   ```bash
   npm install
   ```

3. **Environment variables** (already configured):
   The `.env.local` file is set up with all necessary variables.

4. **Database** (already set up):
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Sample data** (already loaded):
   ```bash
   node scripts/seed.js
   ```

6. **Start development server**:
   ```bash
   npm run dev
   ```

7. **Open in browser**:
   Visit `http://localhost:3000`

## 👥 Sample Accounts

### Instructor Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Capabilities**: Create courses, manage content, view analytics

### Student Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Capabilities**: Enroll in courses, take mock interviews, track progress

## 🎯 Usage Guide

### For Students:
1. **Sign up** or use the sample student account
2. **Browse Courses** - Explore 3 sample courses with different categories
3. **Enroll in Courses** - Join free or paid courses
4. **Take Mock Interviews** - Practice with AI interviewer
5. **Track Progress** - Monitor your learning journey

### For Instructors:
1. **Sign up** as Instructor or use the sample instructor account
2. **Create Courses** - Build comprehensive courses with modules
3. **Add Content** - Upload videos, create lessons, add resources
4. **Manage Students** - View enrollments and progress
5. **Publish Courses** - Make courses available to students

## 🤖 AI Mock Interview System

### Configuration Options:
- **Interview Types**: Technical, Behavioral, HR, Mixed
- **Domains**: Software Engineering, Data Science, Product Management, etc.
- **Difficulty Levels**: Easy, Medium, Hard, Expert
- **Duration**: 15-60 minutes

### Features:
- Real-time video recording
- AI-generated questions based on your profile
- Instant feedback after each answer
- Comprehensive performance analytics
- Improvement recommendations

## 📚 Sample Courses Available

1. **Complete React Development Course** - $99.99 (Intermediate)
2. **AI and Machine Learning Fundamentals** - $149.99 (Beginner)
3. **Free JavaScript Basics** - Free (Beginner)

## 🔧 Current Status

✅ **Fully Working Features:**
- User authentication and registration
- Course browsing and enrollment
- AI mock interview system
- Dashboard and navigation
- Database with sample data
- Responsive design

🔮 **Ready for Enhancement:**
- Payment integration
- Live classes
- Community features
- Advanced analytics

---

**🎉 Your GraphyLMS is ready to use! The application is running at http://localhost:3000**
