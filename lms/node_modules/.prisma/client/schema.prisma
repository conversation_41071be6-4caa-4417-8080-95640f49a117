// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User Management
model User {
  id          String    @id @default(cuid())
  email       String    @unique
  name        String?
  password    String
  role        UserRole  @default(STUDENT)
  avatar      String?
  phone       String?
  dateOfBirth DateTime?
  bio         String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Student specific fields
  studentProfile StudentProfile?

  // Mock Interview Sessions
  interviewSessions MockInterviewSession[]

  // Course management
  coursesCreated Course[]
  enrollments    CourseEnrollment[]
  courseReviews  CourseReview[]

  @@map("users")
}

enum UserRole {
  ADMIN
  INSTRUCTOR
  STUDENT
}

// Student Profile
model StudentProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Academic Information
  education  String?
  experience String?
  skills     String? // JSON string of skills array
  resume     String? // File path to resume

  // Interview Preferences
  preferredDomain String?
  targetRole      String?
  experienceLevel ExperienceLevel @default(BEGINNER)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("student_profiles")
}

enum ExperienceLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

// Mock Interview System
model MockInterviewSession {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Interview Configuration
  interviewType InterviewType
  domain        String // e.g., "Software Engineering", "Data Science"
  difficulty    DifficultyLevel
  duration      Int // in minutes

  // Session Status
  status      InterviewStatus @default(SCHEDULED)
  scheduledAt DateTime?
  startedAt   DateTime?
  completedAt DateTime?

  // AI Configuration
  aiPersonality String? // AI interviewer personality
  customPrompt  String? // Custom interview prompt

  // Results
  overallScore    Float? // 0-100
  feedback        String? // AI generated feedback
  recommendations String? // Improvement suggestions

  // Session Data
  questions InterviewQuestion[]
  responses InterviewResponse[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("mock_interview_sessions")
}

enum InterviewType {
  TECHNICAL
  BEHAVIORAL
  HR
  CASE_STUDY
  MIXED
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
  EXPERT
}

enum InterviewStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// Interview Questions
model InterviewQuestion {
  id        String               @id @default(cuid())
  sessionId String
  session   MockInterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  questionText   String
  questionType   QuestionType
  expectedAnswer String? // For reference
  difficulty     DifficultyLevel
  category       String // e.g., "algorithms", "system design"

  // AI Generated
  aiGenerated Boolean @default(true)

  // Order in interview
  orderIndex Int

  createdAt DateTime @default(now())

  @@map("interview_questions")
}

enum QuestionType {
  TECHNICAL_CODING
  TECHNICAL_THEORY
  BEHAVIORAL
  SITUATIONAL
  CASE_STUDY
}

// Interview Responses
model InterviewResponse {
  id         String               @id @default(cuid())
  sessionId  String
  session    MockInterviewSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  questionId String?

  // Response Data
  responseText String? // Transcribed text
  audioUrl     String? // Recorded audio file
  videoUrl     String? // Recorded video file

  // AI Analysis
  score     Float? // 0-100 for this response
  feedback  String? // AI feedback for this response
  keyPoints String? // JSON string of key points array

  // Timing
  responseTime Int? // Time taken to respond (seconds)
  recordedAt   DateTime @default(now())

  @@map("interview_responses")
}

// Course Management
model Course {
  id           String  @id @default(cuid())
  title        String
  description  String?
  thumbnail    String?
  price        Float?
  isPublished  Boolean @default(false)
  instructorId String
  instructor   User    @relation(fields: [instructorId], references: [id], onDelete: Cascade)

  // Course Details
  category String?
  level    CourseLevel @default(BEGINNER)
  duration Int? // in hours
  language String      @default("English")

  // Course Content
  modules CourseModule[]
  lessons Lesson[]

  // Relations
  enrollments CourseEnrollment[]
  reviews     CourseReview[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("courses")
}

enum CourseLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

// Course Modules
model CourseModule {
  id       String @id @default(cuid())
  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  title       String
  description String?
  orderIndex  Int

  // Module Content
  lessons Lesson[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("course_modules")
}

// Lessons
model Lesson {
  id       String        @id @default(cuid())
  courseId String
  course   Course        @relation(fields: [courseId], references: [id], onDelete: Cascade)
  moduleId String?
  module   CourseModule? @relation(fields: [moduleId], references: [id], onDelete: SetNull)

  title       String
  description String?
  content     String? // Text content
  videoUrl    String? // Video URL
  duration    Int? // in minutes
  orderIndex  Int
  lessonType  LessonType @default(VIDEO)

  // Lesson Resources
  attachments LessonAttachment[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("lessons")
}

enum LessonType {
  VIDEO
  TEXT
  QUIZ
  ASSIGNMENT
  LIVE_SESSION
}

// Lesson Attachments
model LessonAttachment {
  id       String @id @default(cuid())
  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  name     String
  url      String
  fileType String
  fileSize Int? // in bytes

  createdAt DateTime @default(now())

  @@map("lesson_attachments")
}

// Course Reviews
model CourseReview {
  id       String @id @default(cuid())
  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)
  userId   String
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  rating Int // 1-5 stars
  review String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([courseId, userId])
  @@map("course_reviews")
}

// Course Enrollments
model CourseEnrollment {
  id       String @id @default(cuid())
  userId   String
  courseId String
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  enrolledAt DateTime @default(now())
  progress   Float    @default(0) // 0-100

  @@unique([userId, courseId])
  @@map("course_enrollments")
}
