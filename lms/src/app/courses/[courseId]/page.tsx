'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import {
  AcademicCapIcon,
  ClockIcon,
  UserGroupIcon,
  StarIcon,
  PlayIcon,
  BookOpenIcon,
  PencilIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid'

interface Course {
  id: string
  title: string
  description: string
  thumbnail: string
  price: number
  category: string
  level: string
  duration: number
  language: string
  isPublished: boolean
  instructor: {
    id: string
    name: string
    avatar: string
    bio: string
  }
  modules: Array<{
    id: string
    title: string
    description: string
    orderIndex: number
    lessons: Array<{
      id: string
      title: string
      duration: number
      lessonType: string
    }>
  }>
  lessons: Array<{
    id: string
    title: string
    duration: number
    lessonType: string
  }>
  reviews: Array<{
    id: string
    rating: number
    review: string
    user: {
      name: string
      avatar: string
    }
    createdAt: string
  }>
  averageRating: number
  totalDuration: number
  enrollmentCount: number
  lessonCount: number
  isEnrolled: boolean
  isInstructor: boolean
  userProgress: number
}

export default function CourseDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const courseId = params.courseId as string

  const [course, setCourse] = useState<Course | null>(null)
  const [loading, setLoading] = useState(true)
  const [enrolling, setEnrolling] = useState(false)

  useEffect(() => {
    if (courseId) {
      fetchCourse()
    }
  }, [courseId])

  const fetchCourse = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/courses/${courseId}`)
      if (response.ok) {
        const courseData = await response.json()
        setCourse(courseData)
      } else {
        console.error('Failed to fetch course')
      }
    } catch (error) {
      console.error('Error fetching course:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleEnroll = async () => {
    if (!session) {
      alert('Please sign in to enroll')
      return
    }

    setEnrolling(true)
    try {
      const response = await fetch(`/api/courses/${courseId}/enroll`, {
        method: 'POST'
      })

      if (response.ok) {
        fetchCourse() // Refresh course data
        alert('Successfully enrolled!')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to enroll')
      }
    } catch (error) {
      console.error('Error enrolling:', error)
      alert('An error occurred')
    } finally {
      setEnrolling(false)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < Math.floor(rating) ? (
          <StarSolidIcon className="h-5 w-5 text-yellow-400" />
        ) : (
          <StarIcon className="h-5 w-5 text-gray-300" />
        )}
      </span>
    ))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Course not found</h2>
          <Link href="/courses" className="text-indigo-600 hover:text-indigo-800">
            ← Back to Courses
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/courses" className="text-indigo-600 hover:text-indigo-800 mr-4">
                ← Back to Courses
              </Link>
              <AcademicCapIcon className="h-8 w-8 text-indigo-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Course Details</h1>
            </div>
            {course.isInstructor && (
              <Link
                href={`/courses/${courseId}/edit`}
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
              >
                <PencilIcon className="h-5 w-5 mr-2" />
                Edit Course
              </Link>
            )}
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Course Header */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
              {/* Course Image */}
              <div className="h-64 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                {course.thumbnail ? (
                  <img src={course.thumbnail} alt={course.title} className="w-full h-full object-cover" />
                ) : (
                  <AcademicCapIcon className="h-24 w-24 text-white" />
                )}
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {course.level}
                    </span>
                    <span className="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full">
                      {course.category}
                    </span>
                    {!course.isPublished && course.isInstructor && (
                      <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                        Draft
                      </span>
                    )}
                  </div>
                  <div className="text-2xl font-bold text-gray-900">
                    {course.price ? `$${course.price}` : 'Free'}
                  </div>
                </div>

                <h1 className="text-3xl font-bold text-gray-900 mb-4">{course.title}</h1>
                
                <div className="flex items-center space-x-6 mb-6 text-gray-600">
                  <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 mr-2" />
                    {course.totalDuration} minutes
                  </div>
                  <div className="flex items-center">
                    <BookOpenIcon className="h-5 w-5 mr-2" />
                    {course.lessonCount} lessons
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="h-5 w-5 mr-2" />
                    {course.enrollmentCount} students
                  </div>
                  <div className="flex items-center">
                    {renderStars(course.averageRating)}
                    <span className="ml-2">({course.averageRating.toFixed(1)})</span>
                  </div>
                </div>

                <p className="text-gray-700 leading-relaxed">{course.description}</p>
              </div>
            </div>

            {/* Course Content */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Course Content</h2>
              
              {/* Modules */}
              {course.modules.map((module, index) => (
                <div key={module.id} className="border border-gray-200 rounded-lg mb-4">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="font-semibold text-gray-900">
                      Module {index + 1}: {module.title}
                    </h3>
                    {module.description && (
                      <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                    )}
                  </div>
                  <div className="p-4">
                    {module.lessons.map((lesson) => (
                      <div key={lesson.id} className="flex items-center justify-between py-2">
                        <div className="flex items-center">
                          <PlayIcon className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{lesson.title}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {lesson.duration} min
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {/* Standalone Lessons */}
              {course.lessons.length > 0 && (
                <div className="border border-gray-200 rounded-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <h3 className="font-semibold text-gray-900">Additional Lessons</h3>
                  </div>
                  <div className="p-4">
                    {course.lessons.map((lesson) => (
                      <div key={lesson.id} className="flex items-center justify-between py-2">
                        <div className="flex items-center">
                          <PlayIcon className="h-4 w-4 text-gray-400 mr-3" />
                          <span className="text-gray-700">{lesson.title}</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          <ClockIcon className="h-4 w-4 mr-1" />
                          {lesson.duration} min
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Reviews */}
            {course.reviews.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Student Reviews</h2>
                <div className="space-y-6">
                  {course.reviews.slice(0, 5).map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <div className="flex items-center mb-3">
                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                          {review.user.avatar ? (
                            <img src={review.user.avatar} alt={review.user.name} className="w-full h-full rounded-full" />
                          ) : (
                            <span className="text-sm font-medium">{review.user.name[0]}</span>
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{review.user.name}</div>
                          <div className="flex items-center">
                            {renderStars(review.rating)}
                            <span className="ml-2 text-sm text-gray-500">
                              {new Date(review.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                      {review.review && (
                        <p className="text-gray-700">{review.review}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Instructor Info */}
            <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Instructor</h3>
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mr-4">
                  {course.instructor.avatar ? (
                    <img src={course.instructor.avatar} alt={course.instructor.name} className="w-full h-full rounded-full" />
                  ) : (
                    <span className="text-lg font-medium">{course.instructor.name[0]}</span>
                  )}
                </div>
                <div>
                  <div className="font-medium text-gray-900">{course.instructor.name}</div>
                </div>
              </div>
              {course.instructor.bio && (
                <p className="text-gray-600 text-sm">{course.instructor.bio}</p>
              )}
            </div>

            {/* Enrollment Actions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              {course.isEnrolled ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-green-600 font-semibold mb-2">✓ Enrolled</div>
                    <div className="text-sm text-gray-600 mb-4">
                      Progress: {course.userProgress}%
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${course.userProgress}%` }}
                      ></div>
                    </div>
                  </div>
                  <Link
                    href={`/courses/${courseId}/learn`}
                    className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 text-center block"
                  >
                    Continue Learning
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {course.price ? `$${course.price}` : 'Free'}
                    </div>
                    {course.price && (
                      <div className="text-sm text-gray-600 mb-4">
                        One-time payment • Lifetime access
                      </div>
                    )}
                  </div>
                  {session ? (
                    <button
                      onClick={handleEnroll}
                      disabled={enrolling}
                      className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {enrolling ? 'Enrolling...' : 'Enroll Now'}
                    </button>
                  ) : (
                    <Link
                      href="/auth/signin"
                      className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 text-center block"
                    >
                      Sign In to Enroll
                    </Link>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
