export default function CoursesPage() {
  const sampleCourses = [
    {
      id: 1,
      title: "Complete React Development Course",
      description: "Learn React from basics to advanced concepts including hooks, context, and state management.",
      price: 99.99,
      level: "Intermediate",
      category: "Programming",
      instructor: "John Instructor",
      rating: 4.8,
      students: 1250,
      duration: "40 hours"
    },
    {
      id: 2,
      title: "AI and Machine Learning Fundamentals",
      description: "Introduction to artificial intelligence and machine learning concepts with practical examples.",
      price: 149.99,
      level: "Beginner",
      category: "Data Science",
      instructor: "<PERSON> Expert",
      rating: 4.9,
      students: 890,
      duration: "60 hours"
    },
    {
      id: 3,
      title: "Free JavaScript Basics",
      description: "Learn JavaScript fundamentals for free. Perfect for beginners starting their coding journey.",
      price: 0,
      level: "Beginner",
      category: "Programming",
      instructor: "Code Master",
      rating: 4.7,
      students: 2100,
      duration: "20 hours"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <a href="/dashboard" className="text-indigo-600 hover:text-indigo-800 mr-4">
                ← Back to Dashboard
              </a>
              <span className="text-2xl mr-3">📚</span>
              <h1 className="text-2xl font-bold text-gray-900">Courses</h1>
            </div>
            <a
              href="/courses/create"
              className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              <span className="mr-2">➕</span>
              Create Course
            </a>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
              <input
                type="text"
                placeholder="Search courses..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Categories</option>
                <option value="programming">Programming</option>
                <option value="data-science">Data Science</option>
                <option value="design">Design</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option value="">All Prices</option>
                <option value="free">Free</option>
                <option value="paid">Paid</option>
              </select>
            </div>
          </div>
        </div>

        {/* Courses Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sampleCourses.map((course) => (
            <div key={course.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
              {/* Course Thumbnail */}
              <div className="h-48 bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                <span className="text-white text-4xl">📚</span>
              </div>

              <div className="p-6">
                {/* Course Info */}
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{course.title}</h3>
                  <p className="text-gray-600 text-sm line-clamp-2">{course.description}</p>
                </div>

                {/* Instructor */}
                <div className="flex items-center mb-4">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                    <span className="text-xs font-medium">{course.instructor[0]}</span>
                  </div>
                  <span className="text-sm text-gray-700">{course.instructor}</span>
                </div>

                {/* Course Stats */}
                <div className="flex items-center justify-between mb-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <span className="mr-1">⏱️</span>
                    {course.duration}
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">👥</span>
                    {course.students}
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">⭐</span>
                    {course.rating}
                  </div>
                </div>

                {/* Level and Category */}
                <div className="flex items-center justify-between mb-4">
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                    {course.level}
                  </span>
                  <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                    {course.category}
                  </span>
                </div>

                {/* Price and Actions */}
                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-gray-900">
                    {course.price === 0 ? 'Free' : `$${course.price}`}
                  </div>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-sm">
                      View
                    </button>
                    <button className="px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700 text-sm">
                      Enroll
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Course Creation CTA */}
        <div className="mt-12 text-center">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Share Your Knowledge?</h3>
            <p className="text-gray-600 mb-6">
              Create and publish your own courses to help others learn and grow.
            </p>
            <a
              href="/courses/create"
              className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700"
            >
              <span className="mr-2">🎓</span>
              Create Your First Course
            </a>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <div className="flex flex-wrap justify-center gap-4">
            <a href="/" className="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-gray-700">
              🏠 Home
            </a>
            <a href="/dashboard" className="bg-indigo-100 hover:bg-indigo-200 px-4 py-2 rounded-lg text-indigo-700">
              📊 Dashboard
            </a>
            <a href="/mock-interview" className="bg-purple-100 hover:bg-purple-200 px-4 py-2 rounded-lg text-purple-700">
              🎯 Mock Interview
            </a>
          </div>
        </div>
      </main>
    </div>
  )
}
