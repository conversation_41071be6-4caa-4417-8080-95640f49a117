'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  VideoCameraIcon,
  ClockIcon,
  AcademicCapIcon,
  ChartBarIcon,
  PlayIcon
} from '@heroicons/react/24/outline'

export default function MockInterviewPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    interviewType: 'TECHNICAL',
    domain: 'Software Engineering',
    difficulty: 'MEDIUM',
    duration: 30,
    aiPersonality: 'professional',
    customPrompt: ''
  })

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <VideoCameraIcon className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            AI Mock Interview
          </h2>
          <p className="text-gray-600 mb-6">
            Sign in to start practicing with our AI-powered interview system
          </p>
          <Link
            href="/auth/signin"
            className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 font-semibold"
          >
            Sign In to Continue
          </Link>
        </div>
      </div>
    )
  }

  const handleCreateInterview = async () => {
    setIsCreating(true)
    try {
      const response = await fetch('/api/mock-interview/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const data = await response.json()
        router.push(`/mock-interview/${data.session.id}`)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create interview session')
      }
    } catch (error) {
      console.error('Error creating interview:', error)
      alert('An error occurred. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  const interviewTypes = [
    { value: 'TECHNICAL', label: 'Technical Interview', description: 'Coding, algorithms, system design' },
    { value: 'BEHAVIORAL', label: 'Behavioral Interview', description: 'Leadership, teamwork, problem-solving' },
    { value: 'HR', label: 'HR Interview', description: 'General questions, company fit' },
    { value: 'MIXED', label: 'Mixed Interview', description: 'Combination of technical and behavioral' }
  ]

  const domains = [
    'Software Engineering',
    'Data Science',
    'Product Management',
    'DevOps',
    'Frontend Development',
    'Backend Development',
    'Mobile Development',
    'Machine Learning',
    'Cybersecurity',
    'Cloud Architecture'
  ]

  const difficulties = [
    { value: 'EASY', label: 'Easy', description: 'Entry level questions' },
    { value: 'MEDIUM', label: 'Medium', description: 'Mid-level questions' },
    { value: 'HARD', label: 'Hard', description: 'Senior level questions' },
    { value: 'EXPERT', label: 'Expert', description: 'Principal/Staff level questions' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-indigo-600 hover:text-indigo-800 mr-4">
                ← Back to Dashboard
              </Link>
              <VideoCameraIcon className="h-8 w-8 text-indigo-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">AI Mock Interview</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Practice with AI-Powered Interviews
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Get instant feedback, improve your skills, and boost your confidence with our advanced AI interviewer
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <ClockIcon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Real-time Feedback</h3>
            <p className="text-gray-600">Get instant evaluation and suggestions after each answer</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <AcademicCapIcon className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Adaptive Questions</h3>
            <p className="text-gray-600">AI generates questions based on your experience and role</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6 text-center">
            <ChartBarIcon className="h-12 w-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Detailed Analytics</h3>
            <p className="text-gray-600">Track your progress and identify areas for improvement</p>
          </div>
        </div>

        {/* Interview Configuration */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Configure Your Interview</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Interview Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Interview Type
              </label>
              <div className="space-y-3">
                {interviewTypes.map((type) => (
                  <label key={type.value} className="flex items-start">
                    <input
                      type="radio"
                      name="interviewType"
                      value={type.value}
                      checked={formData.interviewType === type.value}
                      onChange={(e) => setFormData({ ...formData, interviewType: e.target.value })}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{type.label}</div>
                      <div className="text-sm text-gray-500">{type.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Domain */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Domain/Field
              </label>
              <select
                value={formData.domain}
                onChange={(e) => setFormData({ ...formData, domain: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                {domains.map((domain) => (
                  <option key={domain} value={domain}>{domain}</option>
                ))}
              </select>
            </div>

            {/* Difficulty */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Difficulty Level
              </label>
              <div className="space-y-3">
                {difficulties.map((diff) => (
                  <label key={diff.value} className="flex items-start">
                    <input
                      type="radio"
                      name="difficulty"
                      value={diff.value}
                      checked={formData.difficulty === diff.value}
                      onChange={(e) => setFormData({ ...formData, difficulty: e.target.value })}
                      className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                    />
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{diff.label}</div>
                      <div className="text-sm text-gray-500">{diff.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* Duration */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Duration (minutes)
              </label>
              <select
                value={formData.duration}
                onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value={15}>15 minutes (3-4 questions)</option>
                <option value={30}>30 minutes (6-7 questions)</option>
                <option value={45}>45 minutes (9-10 questions)</option>
                <option value={60}>60 minutes (12-13 questions)</option>
              </select>
            </div>
          </div>

          {/* Start Interview Button */}
          <div className="mt-8 text-center">
            <button
              onClick={handleCreateInterview}
              disabled={isCreating}
              className="inline-flex items-center px-8 py-4 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Creating Interview...
                </>
              ) : (
                <>
                  <PlayIcon className="h-5 w-5 mr-3" />
                  Start Mock Interview
                </>
              )}
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}
