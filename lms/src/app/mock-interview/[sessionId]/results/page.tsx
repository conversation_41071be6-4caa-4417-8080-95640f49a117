'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import {
  ChartBarIcon,
  TrophyIcon,
  LightBulbIcon,
  ArrowPathIcon,
  HomeIcon
} from '@heroicons/react/24/outline'

interface InterviewResults {
  sessionId: string
  overallScore: number
  feedback: string
  recommendations: string[]
  strengths: string[]
  improvements: string[]
  sessionData: {
    questions: any[]
    responses: any[]
    averageScore: number
    totalQuestions: number
    totalResponses: number
  }
  completedAt: string
}

export default function InterviewResultsPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const params = useParams()
  const sessionId = params.sessionId as string

  const [results, setResults] = useState<InterviewResults | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (sessionId && session) {
      fetchResults()
    }
  }, [sessionId, session])

  const fetchResults = async () => {
    try {
      setIsLoading(true)
      
      // Complete the interview and get results
      const response = await fetch(`/api/mock-interview/${sessionId}/complete`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch results')
      }

      const data = await response.json()
      setResults(data.results)

    } catch (error) {
      console.error('Error fetching results:', error)
      setError('Failed to load interview results. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600'
    if (score >= 6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBgColor = (score: number) => {
    if (score >= 8) return 'bg-green-100'
    if (score >= 6) return 'bg-yellow-100'
    return 'bg-red-100'
  }

  const getPerformanceLevel = (score: number) => {
    if (score >= 9) return 'Excellent'
    if (score >= 8) return 'Very Good'
    if (score >= 7) return 'Good'
    if (score >= 6) return 'Average'
    if (score >= 5) return 'Below Average'
    return 'Needs Improvement'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Generating your interview results...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Results</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.push('/mock-interview')}
            className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 font-semibold"
          >
            Back to Mock Interview
          </button>
        </div>
      </div>
    )
  }

  if (!results) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <TrophyIcon className="h-8 w-8 text-yellow-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Interview Results</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="flex items-center text-gray-600 hover:text-gray-900"
              >
                <HomeIcon className="h-5 w-5 mr-2" />
                Dashboard
              </Link>
              <Link
                href="/mock-interview"
                className="flex items-center bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700"
              >
                <ArrowPathIcon className="h-5 w-5 mr-2" />
                New Interview
              </Link>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overall Score */}
        <div className="bg-white rounded-lg shadow-lg p-8 mb-8 text-center">
          <div className="mb-6">
            <div className={`inline-flex items-center justify-center w-32 h-32 rounded-full ${getScoreBgColor(results.overallScore)} mb-4`}>
              <span className={`text-4xl font-bold ${getScoreColor(results.overallScore)}`}>
                {results.overallScore.toFixed(1)}
              </span>
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {getPerformanceLevel(results.overallScore)}
            </h2>
            <p className="text-gray-600">
              Overall Interview Performance
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{results.sessionData.totalQuestions}</div>
              <div className="text-gray-600">Questions Asked</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">{results.sessionData.totalResponses}</div>
              <div className="text-gray-600">Answers Provided</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-gray-900">
                {results.sessionData.averageScore.toFixed(1)}
              </div>
              <div className="text-gray-600">Average Score</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Detailed Feedback */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center mb-4">
              <ChartBarIcon className="h-6 w-6 text-indigo-600 mr-2" />
              <h3 className="text-xl font-semibold text-gray-900">Detailed Feedback</h3>
            </div>
            <div className="prose prose-sm text-gray-700">
              <p>{results.feedback}</p>
            </div>
          </div>

          {/* Strengths */}
          {results.strengths.length > 0 && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                <TrophyIcon className="h-6 w-6 text-green-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Your Strengths</h3>
              </div>
              <ul className="space-y-2">
                {results.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Areas for Improvement */}
          {results.improvements.length > 0 && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                <LightBulbIcon className="h-6 w-6 text-yellow-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Areas for Improvement</h3>
              </div>
              <ul className="space-y-2">
                {results.improvements.map((improvement, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{improvement}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Recommendations */}
          {results.recommendations.length > 0 && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center mb-4">
                <LightBulbIcon className="h-6 w-6 text-blue-600 mr-2" />
                <h3 className="text-xl font-semibold text-gray-900">Recommendations</h3>
              </div>
              <ul className="space-y-2">
                {results.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <span className="text-gray-700">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Question-by-Question Breakdown */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Question-by-Question Breakdown</h3>
          <div className="space-y-6">
            {results.sessionData.responses.map((response, index) => (
              <div key={index} className="border-l-4 border-gray-200 pl-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Question {index + 1}</h4>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getScoreBgColor(response.score)} ${getScoreColor(response.score)}`}>
                    {response.score}/10
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-2">{response.feedback}</p>
                {response.keyPoints.length > 0 && (
                  <div className="text-xs text-gray-500">
                    <strong>Key points:</strong> {response.keyPoints.join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-4 mt-8">
          <Link
            href="/mock-interview"
            className="bg-indigo-600 text-white px-8 py-3 rounded-lg hover:bg-indigo-700 font-semibold"
          >
            Take Another Interview
          </Link>
          <Link
            href="/dashboard"
            className="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 font-semibold"
          >
            Back to Dashboard
          </Link>
        </div>
      </main>
    </div>
  )
}
