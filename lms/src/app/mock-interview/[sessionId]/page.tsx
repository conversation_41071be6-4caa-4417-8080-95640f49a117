'use client'

import { useState, useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useParams } from 'next/navigation'
import {
  VideoCameraIcon,
  MicrophoneIcon,
  StopIcon,
  PlayIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface Question {
  id: string
  question: string
  type: string
  category: string
  orderIndex: number
}

interface Evaluation {
  score: number
  feedback: string
  keyPoints: string[]
  suggestions: string[]
}

export default function InterviewSessionPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const params = useParams()
  const sessionId = params.sessionId as string

  const [isLoading, setIsLoading] = useState(true)
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [answer, setAnswer] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [evaluation, setEvaluation] = useState<Evaluation | null>(null)
  const [questionNumber, setQuestionNumber] = useState(1)
  const [totalQuestions, setTotalQuestions] = useState(0)
  const [timeRemaining, setTimeRemaining] = useState(0)
  const [sessionStarted, setSessionStarted] = useState(false)
  const [isComplete, setIsComplete] = useState(false)

  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const recordedChunksRef = useRef<Blob[]>([])
  const streamRef = useRef<MediaStream | null>(null)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (sessionId && session) {
      startInterview()
    }
  }, [sessionId, session])

  useEffect(() => {
    return () => {
      // Cleanup
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [])

  const startInterview = async () => {
    try {
      setIsLoading(true)
      
      // Start the interview session
      const response = await fetch(`/api/mock-interview/${sessionId}/start`, {
        method: 'POST',
      })

      if (!response.ok) {
        throw new Error('Failed to start interview')
      }

      const data = await response.json()
      setCurrentQuestion(data.firstQuestion)
      setTotalQuestions(data.config.questionCount)
      setTimeRemaining(data.config.duration * 60) // Convert to seconds
      setSessionStarted(true)

      // Setup camera and microphone
      await setupMedia()
      
      // Start timer
      startTimer()

    } catch (error) {
      console.error('Error starting interview:', error)
      alert('Failed to start interview. Please try again.')
      router.push('/mock-interview')
    } finally {
      setIsLoading(false)
    }
  }

  const setupMedia = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })
      
      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error('Error accessing media devices:', error)
      alert('Please allow camera and microphone access for the interview.')
    }
  }

  const startTimer = () => {
    timerRef.current = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          // Time's up
          handleTimeUp()
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleTimeUp = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    alert('Interview time is up!')
    completeInterview()
  }

  const startRecording = () => {
    if (!streamRef.current) return

    recordedChunksRef.current = []
    
    const mediaRecorder = new MediaRecorder(streamRef.current)
    mediaRecorderRef.current = mediaRecorder

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        recordedChunksRef.current.push(event.data)
      }
    }

    mediaRecorder.start()
    setIsRecording(true)
  }

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
    }
  }

  const submitAnswer = async () => {
    if (!currentQuestion || !answer.trim()) {
      alert('Please provide an answer before submitting.')
      return
    }

    setIsSubmitting(true)
    
    try {
      // Stop recording if active
      if (isRecording) {
        stopRecording()
      }

      // Submit answer
      const response = await fetch(`/api/mock-interview/${sessionId}/answer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId: currentQuestion.id,
          answer: answer.trim(),
          responseTime: 60, // You can calculate actual response time
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to submit answer')
      }

      const data = await response.json()
      
      // Show evaluation
      setEvaluation(data.evaluation)
      
      // Check if interview is complete
      if (data.isComplete) {
        setIsComplete(true)
      } else if (data.nextQuestion) {
        // Prepare for next question
        setTimeout(() => {
          setCurrentQuestion(data.nextQuestion)
          setQuestionNumber(prev => prev + 1)
          setAnswer('')
          setEvaluation(null)
        }, 5000) // Show feedback for 5 seconds
      }

    } catch (error) {
      console.error('Error submitting answer:', error)
      alert('Failed to submit answer. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const completeInterview = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    router.push(`/mock-interview/${sessionId}/results`)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Starting your interview...</p>
        </div>
      </div>
    )
  }

  if (isComplete) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="text-green-600 text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Interview Complete!
          </h2>
          <p className="text-gray-600 mb-6">
            Great job! Your interview has been completed. Click below to view your detailed results.
          </p>
          <button
            onClick={completeInterview}
            className="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 font-semibold"
          >
            View Results
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <VideoCameraIcon className="h-8 w-8 text-indigo-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900">Mock Interview Session</h1>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center text-gray-600">
                <ClockIcon className="h-5 w-5 mr-2" />
                <span className="font-mono text-lg">{formatTime(timeRemaining)}</span>
              </div>
              <div className="text-sm text-gray-500">
                Question {questionNumber} of {totalQuestions}
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Video Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Video Recording</h3>
            <div className="relative">
              <video
                ref={videoRef}
                autoPlay
                muted
                className="w-full h-64 bg-gray-900 rounded-lg"
              />
              {isRecording && (
                <div className="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                  <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                  Recording
                </div>
              )}
            </div>
            <div className="mt-4 flex justify-center space-x-4">
              {!isRecording ? (
                <button
                  onClick={startRecording}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  <MicrophoneIcon className="h-5 w-5 mr-2" />
                  Start Recording
                </button>
              ) : (
                <button
                  onClick={stopRecording}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <StopIcon className="h-5 w-5 mr-2" />
                  Stop Recording
                </button>
              )}
            </div>
          </div>

          {/* Question and Answer Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            {!evaluation ? (
              <>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Question</h3>
                {currentQuestion && (
                  <div className="mb-6">
                    <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
                      <p className="text-blue-800 font-medium">{currentQuestion.question}</p>
                      <p className="text-blue-600 text-sm mt-2">
                        Type: {currentQuestion.type} | Category: {currentQuestion.category}
                      </p>
                    </div>
                  </div>
                )}

                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Answer
                  </label>
                  <textarea
                    value={answer}
                    onChange={(e) => setAnswer(e.target.value)}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Type your answer here..."
                  />
                </div>

                <button
                  onClick={submitAnswer}
                  disabled={isSubmitting || !answer.trim()}
                  className="w-full flex items-center justify-center px-4 py-3 bg-indigo-600 text-white font-semibold rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-5 w-5 mr-2" />
                      Submit Answer
                    </>
                  )}
                </button>
              </>
            ) : (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Feedback</h3>
                <div className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <span className="text-green-800 font-semibold">Score: {evaluation.score}/10</span>
                    </div>
                    <p className="text-green-700">{evaluation.feedback}</p>
                  </div>
                  
                  {evaluation.keyPoints.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-2">Key Points Mentioned:</h4>
                      <ul className="list-disc list-inside text-blue-700">
                        {evaluation.keyPoints.map((point, index) => (
                          <li key={index}>{point}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {evaluation.suggestions.length > 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                      <h4 className="font-semibold text-yellow-800 mb-2">Suggestions for Improvement:</h4>
                      <ul className="list-disc list-inside text-yellow-700">
                        {evaluation.suggestions.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                <p className="text-center text-gray-600 mt-4">
                  Preparing next question...
                </p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
