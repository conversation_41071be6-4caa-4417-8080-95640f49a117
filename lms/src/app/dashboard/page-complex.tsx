'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import {
  VideoCameraIcon,
  UserGroupIcon,
  AcademicCapIcon,
  ChartBarIcon,
  PlayIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline'

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const quickActions = [
    {
      title: 'Start Mock Interview',
      description: 'Practice with AI-powered interview sessions',
      icon: VideoCameraIcon,
      href: '/mock-interview',
      color: 'bg-blue-500',
      featured: true
    },
    {
      title: 'Browse Courses',
      description: 'Explore and enroll in courses',
      icon: BookOpenIcon,
      href: '/courses',
      color: 'bg-green-500'
    },
    ...(session?.user?.role === 'INSTRUCTOR' ? [{
      title: 'Create Course',
      description: 'Create and manage your courses',
      icon: AcademicCapIcon,
      href: '/courses/create',
      color: 'bg-indigo-500'
    }] : []),
    {
      title: 'Profile Settings',
      description: 'Update your profile and preferences',
      icon: UserGroupIcon,
      href: '/profile',
      color: 'bg-purple-500'
    },
    {
      title: 'Progress Analytics',
      description: 'View your learning progress',
      icon: ChartBarIcon,
      href: '/analytics',
      color: 'bg-orange-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome back, {session.user.name}!
              </h1>
              <p className="text-gray-600">Ready to continue your learning journey?</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                {session.user.role}
              </span>
              <button
                onClick={() => router.push('/auth/signout')}
                className="text-gray-500 hover:text-gray-700"
              >
                Sign out
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Featured Action */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg p-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">🎯 Ready for your next interview?</h2>
                <p className="text-blue-100 mb-4">
                  Practice with our AI-powered mock interview system and get instant feedback
                </p>
                <Link
                  href="/mock-interview"
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <PlayIcon className="h-5 w-5 mr-2" />
                  Start Mock Interview
                </Link>
              </div>
              <div className="hidden md:block">
                <VideoIcon className="h-24 w-24 text-blue-200" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickActions.map((action) => (
            <Link
              key={action.title}
              href={action.href}
              className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group"
            >
              <div className="flex items-center mb-4">
                <div className={`${action.color} p-3 rounded-lg`}>
                  <action.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600">
                {action.title}
              </h3>
              <p className="text-gray-600 text-sm">{action.description}</p>
            </Link>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
          </div>
          <div className="p-6">
            <div className="text-center py-8">
              <AcademicCapIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No recent activity yet.</p>
              <p className="text-gray-400 text-sm mt-2">
                Start a mock interview or enroll in a course to see your activity here.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
