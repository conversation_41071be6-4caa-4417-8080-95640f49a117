export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome to GraphyLMS Dashboard!
              </h1>
              <p className="text-gray-600">Your learning journey starts here</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                Demo User
              </span>
              <a href="/" className="text-gray-500 hover:text-gray-700">
                ← Back to Home
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Featured Action */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg shadow-lg p-8 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">🎯 Ready for your next interview?</h2>
                <p className="text-blue-100 mb-4">
                  Practice with our AI-powered mock interview system and get instant feedback
                </p>
                <a
                  href="/mock-interview"
                  className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <span className="mr-2">▶️</span>
                  Start Mock Interview
                </a>
              </div>
              <div className="hidden md:block">
                <span className="text-6xl">🎥</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <a
            href="/mock-interview"
            className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-blue-500 p-3 rounded-lg">
                <span className="text-white text-xl">🤖</span>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600">
              Start Mock Interview
            </h3>
            <p className="text-gray-600 text-sm">Practice with AI-powered interview sessions</p>
          </a>

          <a
            href="/courses"
            className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-green-500 p-3 rounded-lg">
                <span className="text-white text-xl">📚</span>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600">
              Browse Courses
            </h3>
            <p className="text-gray-600 text-sm">Explore and enroll in courses</p>
          </a>

          <a
            href="/courses/create"
            className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-indigo-500 p-3 rounded-lg">
                <span className="text-white text-xl">🎓</span>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600">
              Create Course
            </h3>
            <p className="text-gray-600 text-sm">Create and manage your courses</p>
          </a>

          <a
            href="/profile"
            className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 group"
          >
            <div className="flex items-center mb-4">
              <div className="bg-purple-500 p-3 rounded-lg">
                <span className="text-white text-xl">👤</span>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-indigo-600">
              Profile Settings
            </h3>
            <p className="text-gray-600 text-sm">Update your profile and preferences</p>
          </a>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">5</div>
                <div className="text-gray-600">Courses Available</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🎯</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">12</div>
                <div className="text-gray-600">Mock Interviews</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">⭐</span>
              </div>
              <div className="ml-4">
                <div className="text-2xl font-bold text-gray-900">4.8</div>
                <div className="text-gray-600">Average Rating</div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">🚀 Available Features</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center p-4 bg-green-50 rounded-lg">
                <span className="text-green-600 text-xl mr-4">✅</span>
                <div>
                  <div className="font-medium text-gray-900">AI Mock Interview System</div>
                  <div className="text-sm text-gray-600">Real-time AI interviewer with video recording and feedback</div>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-green-50 rounded-lg">
                <span className="text-green-600 text-xl mr-4">✅</span>
                <div>
                  <div className="font-medium text-gray-900">Course Management</div>
                  <div className="text-sm text-gray-600">Create, publish, and manage comprehensive courses</div>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-green-50 rounded-lg">
                <span className="text-green-600 text-xl mr-4">✅</span>
                <div>
                  <div className="font-medium text-gray-900">Student Enrollment</div>
                  <div className="text-sm text-gray-600">Browse and enroll in courses with progress tracking</div>
                </div>
              </div>
              
              <div className="flex items-center p-4 bg-blue-50 rounded-lg">
                <span className="text-blue-600 text-xl mr-4">🔧</span>
                <div>
                  <div className="font-medium text-gray-900">Authentication System</div>
                  <div className="text-sm text-gray-600">User registration, login, and role-based access control</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-8 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Navigation</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <a href="/" className="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-gray-700">
              🏠 Home
            </a>
            <a href="/auth/signin" className="bg-indigo-100 hover:bg-indigo-200 px-4 py-2 rounded-lg text-indigo-700">
              🔑 Sign In
            </a>
            <a href="/auth/signup" className="bg-green-100 hover:bg-green-200 px-4 py-2 rounded-lg text-green-700">
              📝 Sign Up
            </a>
            <a href="/courses" className="bg-blue-100 hover:bg-blue-200 px-4 py-2 rounded-lg text-blue-700">
              📚 Courses
            </a>
            <a href="/mock-interview" className="bg-purple-100 hover:bg-purple-200 px-4 py-2 rounded-lg text-purple-700">
              🎯 Mock Interview
            </a>
          </div>
        </div>
      </main>
    </div>
  )
}
