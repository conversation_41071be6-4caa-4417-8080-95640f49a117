import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { AIInterviewer } from '@/lib/openai'

export async function POST(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { sessionId } = params
    const {
      questionId,
      answer,
      responseTime,
      audioUrl,
      videoUrl
    } = await request.json()

    // Get the interview session and question
    const interviewSession = await db.mockInterviewSession.findUnique({
      where: {
        id: sessionId,
        userId: session.user.id
      },
      include: {
        user: {
          include: {
            studentProfile: true
          }
        }
      }
    })

    if (!interviewSession) {
      return NextResponse.json(
        { error: 'Interview session not found' },
        { status: 404 }
      )
    }

    const question = await db.interviewQuestion.findUnique({
      where: { id: questionId }
    })

    if (!question) {
      return NextResponse.json(
        { error: 'Question not found' },
        { status: 404 }
      )
    }

    // Initialize AI Interviewer for evaluation
    const aiConfig = {
      domain: interviewSession.domain,
      role: interviewSession.user.studentProfile?.targetRole || 'Software Engineer',
      experience: interviewSession.user.studentProfile?.experienceLevel || 'BEGINNER',
      difficulty: interviewSession.difficulty.toLowerCase() as 'easy' | 'medium' | 'hard',
      duration: interviewSession.duration,
      questionCount: Math.ceil(interviewSession.duration / 5)
    }

    const aiInterviewer = new AIInterviewer(aiConfig)

    // Evaluate the answer using AI
    const evaluation = await aiInterviewer.evaluateAnswer(question.questionText, answer)

    // Save the response to database
    const responseRecord = await db.interviewResponse.create({
      data: {
        sessionId: sessionId,
        questionId: questionId,
        responseText: answer,
        audioUrl,
        videoUrl,
        score: evaluation.score,
        feedback: evaluation.feedback,
        keyPoints: JSON.stringify(evaluation.keyPoints),
        responseTime,
        recordedAt: new Date()
      }
    })

    // Check if we need to generate next question
    const currentQuestionCount = await db.interviewQuestion.count({
      where: { sessionId }
    })

    const maxQuestions = aiConfig.questionCount
    let nextQuestion = null

    if (currentQuestionCount < maxQuestions) {
      // Generate next question
      const nextQuestionData = await aiInterviewer.generateQuestion(currentQuestionCount + 1)
      
      const nextQuestionRecord = await db.interviewQuestion.create({
        data: {
          sessionId: sessionId,
          questionText: nextQuestionData.question,
          questionType: nextQuestionData.type.toUpperCase() as any,
          expectedAnswer: nextQuestionData.expectedAnswer,
          difficulty: nextQuestionData.difficulty.toUpperCase() as any,
          category: nextQuestionData.category,
          orderIndex: currentQuestionCount + 1,
          aiGenerated: true
        }
      })

      nextQuestion = {
        id: nextQuestionRecord.id,
        question: nextQuestionData.question,
        type: nextQuestionData.type,
        category: nextQuestionData.category,
        orderIndex: currentQuestionCount + 1
      }
    }

    return NextResponse.json({
      message: 'Answer submitted successfully',
      evaluation: {
        score: evaluation.score,
        feedback: evaluation.feedback,
        keyPoints: evaluation.keyPoints,
        suggestions: evaluation.suggestions
      },
      nextQuestion,
      isComplete: currentQuestionCount >= maxQuestions
    })

  } catch (error) {
    console.error('Error processing answer:', error)
    return NextResponse.json(
      { error: 'Failed to process answer' },
      { status: 500 }
    )
  }
}
