import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { AIInterviewer } from '@/lib/openai'
import { InterviewStatus } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { sessionId } = params

    // Get the interview session
    const interviewSession = await db.mockInterviewSession.findUnique({
      where: {
        id: sessionId,
        userId: session.user.id
      },
      include: {
        user: {
          include: {
            studentProfile: true
          }
        }
      }
    })

    if (!interviewSession) {
      return NextResponse.json(
        { error: 'Interview session not found' },
        { status: 404 }
      )
    }

    if (interviewSession.status !== InterviewStatus.SCHEDULED) {
      return NextResponse.json(
        { error: 'Interview session already started or completed' },
        { status: 400 }
      )
    }

    // Update session status to IN_PROGRESS
    await db.mockInterviewSession.update({
      where: { id: sessionId },
      data: {
        status: InterviewStatus.IN_PROGRESS,
        startedAt: new Date()
      }
    })

    // Initialize AI Interviewer
    const aiConfig = {
      domain: interviewSession.domain,
      role: interviewSession.user.studentProfile?.targetRole || 'Software Engineer',
      experience: interviewSession.user.studentProfile?.experienceLevel || 'BEGINNER',
      difficulty: interviewSession.difficulty.toLowerCase() as 'easy' | 'medium' | 'hard',
      duration: interviewSession.duration,
      questionCount: Math.ceil(interviewSession.duration / 5) // ~5 minutes per question
    }

    const aiInterviewer = new AIInterviewer(aiConfig)

    // Generate first question
    const firstQuestion = await aiInterviewer.generateQuestion(1)

    // Save the first question to database
    const questionRecord = await db.interviewQuestion.create({
      data: {
        sessionId: sessionId,
        questionText: firstQuestion.question,
        questionType: firstQuestion.type.toUpperCase() as any,
        expectedAnswer: firstQuestion.expectedAnswer,
        difficulty: firstQuestion.difficulty.toUpperCase() as any,
        category: firstQuestion.category,
        orderIndex: 1,
        aiGenerated: true
      }
    })

    return NextResponse.json({
      message: 'Interview started successfully',
      session: {
        id: sessionId,
        status: 'IN_PROGRESS',
        startedAt: new Date()
      },
      firstQuestion: {
        id: questionRecord.id,
        question: firstQuestion.question,
        type: firstQuestion.type,
        category: firstQuestion.category,
        orderIndex: 1
      },
      config: aiConfig
    })

  } catch (error) {
    console.error('Error starting interview session:', error)
    return NextResponse.json(
      { error: 'Failed to start interview session' },
      { status: 500 }
    )
  }
}
