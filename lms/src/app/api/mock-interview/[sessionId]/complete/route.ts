import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { AIInterviewer } from '@/lib/openai'
import { InterviewStatus } from '@prisma/client'

export async function POST(
  request: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { sessionId } = params

    // Get the interview session with all related data
    const interviewSession = await db.mockInterviewSession.findUnique({
      where: {
        id: sessionId,
        userId: session.user.id
      },
      include: {
        user: {
          include: {
            studentProfile: true
          }
        },
        questions: {
          orderBy: { orderIndex: 'asc' }
        },
        responses: {
          orderBy: { recordedAt: 'asc' }
        }
      }
    })

    if (!interviewSession) {
      return NextResponse.json(
        { error: 'Interview session not found' },
        { status: 404 }
      )
    }

    // Initialize AI Interviewer for final feedback
    const aiConfig = {
      domain: interviewSession.domain,
      role: interviewSession.user.studentProfile?.targetRole || 'Software Engineer',
      experience: interviewSession.user.studentProfile?.experienceLevel || 'BEGINNER',
      difficulty: interviewSession.difficulty.toLowerCase() as 'easy' | 'medium' | 'hard',
      duration: interviewSession.duration,
      questionCount: interviewSession.questions.length
    }

    const aiInterviewer = new AIInterviewer(aiConfig)

    // Prepare session data for final evaluation
    const sessionData = {
      questions: interviewSession.questions.map(q => ({
        question: q.questionText,
        type: q.questionType,
        category: q.category
      })),
      responses: interviewSession.responses.map(r => ({
        answer: r.responseText,
        score: r.score,
        feedback: r.feedback,
        keyPoints: r.keyPoints ? JSON.parse(r.keyPoints) : [],
        responseTime: r.responseTime
      })),
      averageScore: interviewSession.responses.reduce((sum, r) => sum + (r.score || 0), 0) / interviewSession.responses.length,
      totalQuestions: interviewSession.questions.length,
      totalResponses: interviewSession.responses.length
    }

    // Generate final feedback
    const finalFeedback = await aiInterviewer.generateFinalFeedback(sessionData)

    // Update session with final results
    const updatedSession = await db.mockInterviewSession.update({
      where: { id: sessionId },
      data: {
        status: InterviewStatus.COMPLETED,
        completedAt: new Date(),
        overallScore: finalFeedback.overallScore,
        feedback: finalFeedback.feedback,
        recommendations: JSON.stringify(finalFeedback.recommendations)
      }
    })

    return NextResponse.json({
      message: 'Interview completed successfully',
      results: {
        sessionId: sessionId,
        overallScore: finalFeedback.overallScore,
        feedback: finalFeedback.feedback,
        recommendations: finalFeedback.recommendations,
        strengths: finalFeedback.strengths,
        improvements: finalFeedback.improvements,
        sessionData: sessionData,
        completedAt: updatedSession.completedAt
      }
    })

  } catch (error) {
    console.error('Error completing interview session:', error)
    return NextResponse.json(
      { error: 'Failed to complete interview session' },
      { status: 500 }
    )
  }
}
