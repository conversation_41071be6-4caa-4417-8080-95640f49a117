import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { InterviewType, DifficultyLevel, InterviewStatus } from '@prisma/client'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const {
      interviewType,
      domain,
      difficulty,
      duration,
      aiPersonality,
      customPrompt
    } = await request.json()

    // Validate required fields
    if (!interviewType || !domain || !difficulty || !duration) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create mock interview session
    const session_data = await db.mockInterviewSession.create({
      data: {
        userId: session.user.id,
        interviewType: interviewType as InterviewType,
        domain,
        difficulty: difficulty as DifficultyLevel,
        duration: parseInt(duration),
        status: InterviewStatus.SCHEDULED,
        aiPersonality,
        customPrompt,
        scheduledAt: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            studentProfile: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Mock interview session created successfully',
      session: session_data
    })

  } catch (error) {
    console.error('Error creating mock interview session:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
