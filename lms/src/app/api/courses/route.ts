import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { CourseLevel } from '@prisma/client'

// GET /api/courses - Get all courses or user's courses
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'all', 'my-courses', 'created'
    const category = searchParams.get('category')
    const level = searchParams.get('level')
    const search = searchParams.get('search')

    let whereClause: any = {}

    // Filter by published status for public courses
    if (type !== 'created') {
      whereClause.isPublished = true
    }

    // Filter by instructor's courses
    if (type === 'created' && session?.user?.id) {
      whereClause.instructorId = session.user.id
    }

    // Filter by enrolled courses
    if (type === 'my-courses' && session?.user?.id) {
      whereClause.enrollments = {
        some: {
          userId: session.user.id
        }
      }
    }

    // Additional filters
    if (category) {
      whereClause.category = category
    }

    if (level) {
      whereClause.level = level as CourseLevel
    }

    if (search) {
      whereClause.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    const courses = await db.course.findMany({
      where: whereClause,
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        enrollments: {
          select: {
            id: true,
            userId: true
          }
        },
        reviews: {
          select: {
            rating: true
          }
        },
        _count: {
          select: {
            lessons: true,
            enrollments: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Calculate average rating and enrollment count
    const coursesWithStats = courses.map(course => ({
      ...course,
      averageRating: course.reviews.length > 0 
        ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length 
        : 0,
      enrollmentCount: course._count.enrollments,
      lessonCount: course._count.lessons,
      isEnrolled: session?.user?.id ? course.enrollments.some(e => e.userId === session.user.id) : false
    }))

    return NextResponse.json({
      courses: coursesWithStats,
      total: coursesWithStats.length
    })

  } catch (error) {
    console.error('Error fetching courses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/courses - Create a new course
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is instructor or admin
    if (session.user.role !== 'INSTRUCTOR' && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Only instructors can create courses' },
        { status: 403 }
      )
    }

    const {
      title,
      description,
      category,
      level,
      price,
      duration,
      language,
      thumbnail
    } = await request.json()

    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { error: 'Course title is required' },
        { status: 400 }
      )
    }

    // Create course
    const course = await db.course.create({
      data: {
        title,
        description,
        category,
        level: level as CourseLevel || 'BEGINNER',
        price: price ? parseFloat(price) : null,
        duration: duration ? parseInt(duration) : null,
        language: language || 'English',
        thumbnail,
        instructorId: session.user.id,
        isPublished: false
      },
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Course created successfully',
      course
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
