import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'

// POST /api/courses/[courseId]/enroll - Enroll in course
export async function POST(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { courseId } = params

    // Check if course exists and is published
    const course = await db.course.findUnique({
      where: { id: courseId },
      include: {
        enrollments: {
          where: { userId: session.user.id }
        }
      }
    })

    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    if (!course.isPublished) {
      return NextResponse.json(
        { error: 'Course is not available for enrollment' },
        { status: 400 }
      )
    }

    // Check if already enrolled
    if (course.enrollments.length > 0) {
      return NextResponse.json(
        { error: 'Already enrolled in this course' },
        { status: 400 }
      )
    }

    // Create enrollment
    const enrollment = await db.courseEnrollment.create({
      data: {
        userId: session.user.id,
        courseId: courseId,
        progress: 0
      },
      include: {
        course: {
          select: {
            id: true,
            title: true,
            thumbnail: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Successfully enrolled in course',
      enrollment
    })

  } catch (error) {
    console.error('Error enrolling in course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/courses/[courseId]/enroll - Unenroll from course
export async function DELETE(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { courseId } = params

    // Find and delete enrollment
    const enrollment = await db.courseEnrollment.findFirst({
      where: {
        userId: session.user.id,
        courseId: courseId
      }
    })

    if (!enrollment) {
      return NextResponse.json(
        { error: 'Not enrolled in this course' },
        { status: 400 }
      )
    }

    await db.courseEnrollment.delete({
      where: { id: enrollment.id }
    })

    return NextResponse.json({
      message: 'Successfully unenrolled from course'
    })

  } catch (error) {
    console.error('Error unenrolling from course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
