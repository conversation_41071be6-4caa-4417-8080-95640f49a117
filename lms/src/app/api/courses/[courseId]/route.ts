import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { CourseLevel } from '@prisma/client'

// GET /api/courses/[courseId] - Get course details
export async function GET(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    const { courseId } = params

    const course = await db.course.findUnique({
      where: { id: courseId },
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            avatar: true,
            bio: true
          }
        },
        modules: {
          include: {
            lessons: {
              orderBy: { orderIndex: 'asc' }
            }
          },
          orderBy: { orderIndex: 'asc' }
        },
        lessons: {
          where: { moduleId: null }, // Lessons not in modules
          orderBy: { orderIndex: 'asc' }
        },
        enrollments: {
          select: {
            id: true,
            userId: true,
            enrolledAt: true,
            progress: true
          }
        },
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            enrollments: true,
            lessons: true
          }
        }
      }
    })

    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    // Check if user can access this course
    const isInstructor = session?.user?.id === course.instructorId
    const isEnrolled = session?.user?.id ? course.enrollments.some(e => e.userId === session.user.id) : false
    const canAccess = course.isPublished || isInstructor

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Course not available' },
        { status: 403 }
      )
    }

    // Calculate course statistics
    const averageRating = course.reviews.length > 0 
      ? course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length 
      : 0

    const totalDuration = course.lessons.reduce((sum, lesson) => sum + (lesson.duration || 0), 0) +
      course.modules.reduce((sum, module) => 
        sum + module.lessons.reduce((lessonSum, lesson) => lessonSum + (lesson.duration || 0), 0), 0
      )

    const userEnrollment = isEnrolled ? course.enrollments.find(e => e.userId === session?.user?.id) : null

    return NextResponse.json({
      ...course,
      averageRating,
      totalDuration,
      enrollmentCount: course._count.enrollments,
      lessonCount: course._count.lessons,
      isEnrolled,
      isInstructor,
      userProgress: userEnrollment?.progress || 0
    })

  } catch (error) {
    console.error('Error fetching course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/courses/[courseId] - Update course
export async function PUT(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { courseId } = params

    // Check if user owns this course
    const existingCourse = await db.course.findUnique({
      where: { id: courseId }
    })

    if (!existingCourse) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    if (existingCourse.instructorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'You can only edit your own courses' },
        { status: 403 }
      )
    }

    const {
      title,
      description,
      category,
      level,
      price,
      duration,
      language,
      thumbnail,
      isPublished
    } = await request.json()

    // Update course
    const updatedCourse = await db.course.update({
      where: { id: courseId },
      data: {
        title,
        description,
        category,
        level: level as CourseLevel,
        price: price ? parseFloat(price) : null,
        duration: duration ? parseInt(duration) : null,
        language,
        thumbnail,
        isPublished
      },
      include: {
        instructor: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    })

    return NextResponse.json({
      message: 'Course updated successfully',
      course: updatedCourse
    })

  } catch (error) {
    console.error('Error updating course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/courses/[courseId] - Delete course
export async function DELETE(
  request: NextRequest,
  { params }: { params: { courseId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { courseId } = params

    // Check if user owns this course
    const existingCourse = await db.course.findUnique({
      where: { id: courseId }
    })

    if (!existingCourse) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      )
    }

    if (existingCourse.instructorId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'You can only delete your own courses' },
        { status: 403 }
      )
    }

    // Delete course (cascade will handle related records)
    await db.course.delete({
      where: { id: courseId }
    })

    return NextResponse.json({
      message: 'Course deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting course:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
