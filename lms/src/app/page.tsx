export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            🎓 GraphyLMS
          </h1>
          <h2 className="text-2xl text-indigo-600 mb-8">
            AI-Powered Learning Management System
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Experience the future of education with our comprehensive LMS featuring 
            AI mock interviews, personalized learning paths, and real-time progress tracking.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/auth/signup"
              className="bg-indigo-600 text-white px-8 py-4 rounded-lg hover:bg-indigo-700 font-semibold text-lg"
            >
              Start Learning Today
            </a>
            <a
              href="/auth/signin"
              className="border border-indigo-600 text-indigo-600 px-8 py-4 rounded-lg hover:bg-indigo-50 font-semibold text-lg"
            >
              Sign In
            </a>
          </div>
        </div>

        {/* Features */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="bg-indigo-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🤖</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">AI Mock Interviews</h3>
            <p className="text-gray-600">
              Practice with our AI interviewer and get instant feedback on your performance
            </p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📚</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Course Management</h3>
            <p className="text-gray-600">
              Access comprehensive courses with video content, assignments, and assessments
            </p>
          </div>
          
          <div className="text-center p-6">
            <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📊</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Progress Tracking</h3>
            <p className="text-gray-600">
              Monitor your learning journey with detailed analytics and performance insights
            </p>
          </div>
        </div>

        {/* Status */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-green-600 mb-4">✅ Application Status: WORKING</h3>
            <p className="text-gray-600 mb-4">
              The GraphyLMS application is now running successfully!
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">✅ Working Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Server running on localhost:3000</li>
                  <li>• Database connected and seeded</li>
                  <li>• Authentication system ready</li>
                  <li>• Course management system</li>
                  <li>• AI mock interview system</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">🎯 Sample Accounts:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Student: <EMAIL></li>
                  <li>• Instructor: <EMAIL></li>
                  <li>• Password: password123</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
