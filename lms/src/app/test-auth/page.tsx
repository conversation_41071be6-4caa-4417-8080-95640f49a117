'use client'

import { useSession } from 'next-auth/react'

export default function TestAuthPage() {
  const { data: session, status } = useSession()

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Authentication Test</h1>
        
        <div className="space-y-4">
          <div>
            <strong>Status:</strong> {status}
          </div>
          
          <div>
            <strong>Session:</strong>
            <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
              {JSON.stringify(session, null, 2)}
            </pre>
          </div>
          
          <div className="space-y-2">
            <a 
              href="/auth/signin" 
              className="block w-full text-center bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Sign In
            </a>
            <a 
              href="/auth/signup" 
              className="block w-full text-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Sign Up
            </a>
            <a 
              href="/dashboard" 
              className="block w-full text-center bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
