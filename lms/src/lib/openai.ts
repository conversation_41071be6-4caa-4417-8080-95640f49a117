import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface InterviewQuestion {
  question: string
  type: 'technical' | 'behavioral' | 'situational'
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
  expectedAnswer?: string
}

export interface InterviewConfig {
  domain: string
  role: string
  experience: string
  difficulty: 'easy' | 'medium' | 'hard'
  duration: number
  questionCount: number
}

export class AIInterviewer {
  private config: InterviewConfig
  private conversationHistory: Array<{ role: 'system' | 'user' | 'assistant', content: string }> = []

  constructor(config: InterviewConfig) {
    this.config = config
    this.initializeInterviewer()
  }

  private initializeInterviewer() {
    const systemPrompt = `You are an experienced ${this.config.domain} interviewer conducting a ${this.config.difficulty} level interview for a ${this.config.role} position. 

The candidate has ${this.config.experience} experience level. 

Your role:
1. Ask relevant, progressive questions based on the role and experience level
2. Provide constructive feedback after each answer
3. Maintain a professional but friendly tone
4. Ask follow-up questions when appropriate
5. Cover both technical and behavioral aspects

Interview Guidelines:
- Start with easier questions and gradually increase difficulty
- Ask ${this.config.questionCount} questions total
- Each question should be clear and specific
- Provide immediate feedback on answers
- Score each answer from 1-10
- Give improvement suggestions

Begin the interview with a warm greeting and the first question.`

    this.conversationHistory.push({
      role: 'system',
      content: systemPrompt
    })
  }

  async generateQuestion(questionNumber: number): Promise<InterviewQuestion> {
    try {
      const prompt = `Generate interview question #${questionNumber} for a ${this.config.role} position in ${this.config.domain}. 
      
      Experience level: ${this.config.experience}
      Difficulty: ${this.config.difficulty}
      
      Return a JSON object with:
      - question: the interview question
      - type: "technical", "behavioral", or "situational"
      - difficulty: "${this.config.difficulty}"
      - category: specific category (e.g., "algorithms", "system design", "leadership")
      - expectedAnswer: brief expected answer outline
      
      Make sure the question is appropriate for the experience level and progressively challenging.`

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          ...this.conversationHistory,
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 500
      })

      const content = response.choices[0]?.message?.content
      if (!content) throw new Error('No response from OpenAI')

      // Try to parse JSON response
      try {
        const questionData = JSON.parse(content)
        return questionData as InterviewQuestion
      } catch {
        // Fallback if not JSON
        return {
          question: content,
          type: 'technical',
          difficulty: this.config.difficulty,
          category: this.config.domain,
        }
      }
    } catch (error) {
      console.error('Error generating question:', error)
      throw new Error('Failed to generate interview question')
    }
  }

  async evaluateAnswer(question: string, answer: string): Promise<{
    score: number
    feedback: string
    keyPoints: string[]
    suggestions: string[]
  }> {
    try {
      const prompt = `Evaluate this interview answer:

Question: "${question}"
Answer: "${answer}"

Provide evaluation as JSON with:
- score: number from 1-10
- feedback: constructive feedback (2-3 sentences)
- keyPoints: array of key points mentioned by candidate
- suggestions: array of improvement suggestions

Consider:
- Technical accuracy
- Communication clarity
- Completeness of answer
- Problem-solving approach
- Experience level: ${this.config.experience}`

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          ...this.conversationHistory,
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 600
      })

      const content = response.choices[0]?.message?.content
      if (!content) throw new Error('No response from OpenAI')

      // Add to conversation history
      this.conversationHistory.push(
        { role: 'user', content: `Question: ${question}\nAnswer: ${answer}` },
        { role: 'assistant', content: content }
      )

      try {
        return JSON.parse(content)
      } catch {
        // Fallback evaluation
        return {
          score: 7,
          feedback: content,
          keyPoints: [],
          suggestions: ['Practice more technical questions', 'Improve communication clarity']
        }
      }
    } catch (error) {
      console.error('Error evaluating answer:', error)
      throw new Error('Failed to evaluate answer')
    }
  }

  async generateFinalFeedback(sessionData: any): Promise<{
    overallScore: number
    feedback: string
    recommendations: string[]
    strengths: string[]
    improvements: string[]
  }> {
    try {
      const prompt = `Generate final interview feedback based on the complete session:

Session Data: ${JSON.stringify(sessionData, null, 2)}

Provide comprehensive feedback as JSON with:
- overallScore: average score (1-10)
- feedback: detailed overall feedback (3-4 sentences)
- recommendations: array of specific recommendations
- strengths: array of candidate's strengths
- improvements: array of areas for improvement

Be constructive and specific in your feedback.`

      const response = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          ...this.conversationHistory,
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 800
      })

      const content = response.choices[0]?.message?.content
      if (!content) throw new Error('No response from OpenAI')

      try {
        return JSON.parse(content)
      } catch {
        return {
          overallScore: 7,
          feedback: content,
          recommendations: [],
          strengths: [],
          improvements: []
        }
      }
    } catch (error) {
      console.error('Error generating final feedback:', error)
      throw new Error('Failed to generate final feedback')
    }
  }
}

export default openai
